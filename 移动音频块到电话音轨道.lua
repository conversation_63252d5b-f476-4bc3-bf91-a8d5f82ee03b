-- 脚本名称：移动选中音频块到电话音轨道
-- 功能：把选中的音频块垂直移动到名称包含电话或电话音的轨道上
-- Author: Generated Script
-- Version: 1.0

-- 主函数
function main()
    -- 获取选中的媒体项数量
    local selected_items_count = reaper.CountSelectedMediaItems(0)
    
    if selected_items_count == 0 then
        reaper.ShowMessageBox("没有选中任何音频块", "提示", 0)
        return
    end
    
    -- 查找名称包含"电话"或"电话音"的轨道
    local target_track = nil
    local track_count = reaper.CountTracks(0)
    
    for i = 0, track_count - 1 do
        local track = reaper.GetTrack(0, i)
        local retval, track_name = reaper.GetSetMediaTrackInfo_String(track, "P_NAME", "", false)
        
        if track_name and (string.find(track_name, "电话") or string.find(track_name, "电话音")) then
            target_track = track
            break
        end
    end
    
    if not target_track then
        -- 自动创建名为"电话音"的轨道
        reaper.InsertTrackAtIndex(track_count, false)
        target_track = reaper.GetTrack(0, track_count)
        reaper.GetSetMediaTrackInfo_String(target_track, "P_NAME", "电话音", true)
        -- 设置轨道颜色为深蓝色
        reaper.SetTrackColor(target_track, reaper.ColorToNative(0, 0, 139)|0x1000000)
        
        -- 自动加载Pro-Q 3 (FabFilter)插件效果（不显示界面）
        local eq_fx_index = reaper.TrackFX_AddByName(target_track, "Pro-Q 3 (FabFilter)", false, -1)
        if eq_fx_index >= 0 then
            -- 强制关闭插件界面
            reaper.TrackFX_SetOpen(target_track, eq_fx_index, false)
            reaper.TrackFX_Show(target_track, eq_fx_index, 0)  -- 0 = 隐藏界面
            
            -- 设置Pro-Q 3为Phone预设（参数索引可能需要调整）
            -- 通常预设参数在索引0或特定位置，Phone预设可能对应特定数值
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 0, 1)  -- Phone预设参数
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 2, 0.49345)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 7, 0.4592)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 8, 0.25)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 9, 0.35)

            reaper.TrackFX_SetParam(target_track, eq_fx_index, 13, 1)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 15, 0.734064)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 20, 0.4592)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 21, 0.5)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 22, 0.35)

            reaper.TrackFX_SetParam(target_track, eq_fx_index, 26, 1)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 28, 0.63465)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 29, 0.6068)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 33, 0.64)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 35, 0.15)

            reaper.TrackFX_SetParam(target_track, eq_fx_index, 39, 1)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 41, 0.697238)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 42, 0.6005)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 46, 0.75)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 48, 0.15)

            reaper.TrackFX_SetParam(target_track, eq_fx_index, 52, 1)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 54, 0.567686)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 55, 0.5775)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 59, 0.68)
            reaper.TrackFX_SetParam(target_track, eq_fx_index, 61, 0.15)
                            
        end
        
        -- 自动加载Pro-C 2 (FabFilter)插件效果（不显示界面）
        local comp_fx_index = reaper.TrackFX_AddByName(target_track, "Pro-C 2 (FabFilter)", false, -1)
        if comp_fx_index >= 0 then
            -- 强制关闭插件界面
            reaper.TrackFX_SetOpen(target_track, comp_fx_index, false)
            reaper.TrackFX_Show(target_track, comp_fx_index, 0)  -- 0 = 隐藏界面
        end
    end
    
    -- 先收集所有选中的媒体项到表中
    local selected_items = {}
    for i = 0, selected_items_count - 1 do
        local item = reaper.GetSelectedMediaItem(0, i)
        if item then
            table.insert(selected_items, item)
        end
    end
    
    -- 移动收集到的媒体项到目标轨道
    local moved_items = 0
    
    for i, item in ipairs(selected_items) do
        -- 获取当前项的位置信息
        local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
        local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        
        -- 移动项到目标轨道
        local success = reaper.MoveMediaItemToTrack(item, target_track)
        
        if success then
            moved_items = moved_items + 1
        end
    end
    
    if moved_items > 0 then
        -- 更新项目显示
        reaper.UpdateArrange()
        reaper.UpdateTimeline()
    end
end

-- 开始撤销块
reaper.Undo_BeginBlock()

-- 预选择所有要移动的项目
reaper.PreventUIRefresh(1)

-- 执行主函数
main()

-- 恢复UI刷新
reaper.PreventUIRefresh(-1)

-- 结束撤销块
reaper.Undo_EndBlock("移动选中音频块到电话音轨道", -1)