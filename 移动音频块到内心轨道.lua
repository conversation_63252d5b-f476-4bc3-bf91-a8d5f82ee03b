-- 脚本名称：移动选中音频块到内心OS轨道
-- 功能：把选中的音频块垂直移动到名称为内心OS（包含这两个文字）的轨道上
-- Author: Generated Script
-- Version: 1.0

-- 主函数
function main()
    -- 获取选中的媒体项数量
    local selected_items_count = reaper.CountSelectedMediaItems(0)
    
    if selected_items_count == 0 then
        reaper.ShowMessageBox("没有选中任何音频块", "提示", 0)
        return
    end
    
    -- 查找名称包含"内心"或"OS"的轨道
    local target_track = nil
    local track_count = reaper.CountTracks(0)
    
    for i = 0, track_count - 1 do
        local track = reaper.GetTrack(0, i)
        local retval, track_name = reaper.GetSetMediaTrackInfo_String(track, "P_NAME", "", false)
        
        if track_name and (string.find(track_name, "内心") or string.find(track_name, "OS")) then
            target_track = track
            break
        end
    end
    
    if not target_track then
        -- 自动创建名为"内心os"的轨道
        reaper.InsertTrackAtIndex(track_count, false)
        target_track = reaper.GetTrack(0, track_count)
        reaper.GetSetMediaTrackInfo_String(target_track, "P_NAME", "内心os", true)
        -- 设置轨道颜色为红色
        reaper.SetTrackColor(target_track, reaper.ColorToNative(255, 0, 0)|0x1000000)
    end
    
    -- 检查轨道是否已有FX，如果没有则添加Pro-R插件
    local fx_count = reaper.TrackFX_GetCount(target_track)
    if fx_count == 0 then
        -- 自动加载Pro-R (FabFilter)插件效果（不显示界面）
        local fx_index = reaper.TrackFX_AddByName(target_track, "Pro-R (FabFilter)", false, -1)
        if fx_index >= 0 then
            -- 强制关闭插件界面
            reaper.TrackFX_SetOpen(target_track, fx_index, false)
            reaper.TrackFX_Show(target_track, fx_index, 0)  -- 0 = 隐藏界面
            
            -- 设置默认参数
            -- Space: 1.5s (参数索引可能需要调整)
            reaper.TrackFX_SetParam(target_track, fx_index, 0, 0.3417)  -- Space参数，1.5s对应0.341
            -- Brightness: 60% 
            reaper.TrackFX_SetParam(target_track, fx_index, 2, 0.6)   -- Brightness参数，60%对应0.6
            -- Mix: 25%
            reaper.TrackFX_SetParam(target_track, fx_index, 6, 0.25)  -- Mix参数，25%对应0.25
        end
    end
    
    -- 先收集所有选中的媒体项到表中
    local selected_items = {}
    for i = 0, selected_items_count - 1 do
        local item = reaper.GetSelectedMediaItem(0, i)
        if item then
            table.insert(selected_items, item)
        end
    end
    
    -- 移动收集到的媒体项到目标轨道
    local moved_items = 0
    
    for i, item in ipairs(selected_items) do
        -- 获取当前项的位置信息
        local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
        local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        
        -- 移动项到目标轨道
        local success = reaper.MoveMediaItemToTrack(item, target_track)
        
        if success then
            moved_items = moved_items + 1
        end
    end
    
    if moved_items > 0 then
        -- 更新项目显示
        reaper.UpdateArrange()
        reaper.UpdateTimeline()
    end
end

-- 开始撤销块
reaper.Undo_BeginBlock()

-- 预选择所有要移动的项目
reaper.PreventUIRefresh(1)

-- 执行主函数
main()

-- 恢复UI刷新
reaper.PreventUIRefresh(-1)

-- 结束撤销块
reaper.Undo_EndBlock("移动选中音频块到内心OS轨道", -1)