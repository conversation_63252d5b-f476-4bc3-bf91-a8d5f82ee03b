-- 脚本名称：删除当前轨道编辑光标左边的音频块
-- 功能：删除当前轨道编辑光标左边的音频块，但范围限制在"X秒未对轨分隔线"标记(ID:99999)右边的音频块
-- Author: Generated Script
-- Version: 1.0

-- 主函数
function main()
    -- 获取当前编辑光标位置
    local cursor_pos = reaper.GetCursorPosition()
    
    -- 查找"X秒未对轨分隔线"标记(ID:99999)
    local marker_pos = nil
    local marker_count = reaper.CountProjectMarkers(0)
    
    for i = 0, marker_count - 1 do
        local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(i)
        if not isrgn and name and (name == "X秒未对轨分隔线" or markrgnindexnumber == 99999) then
            marker_pos = pos
            break
        end
    end
    
    if not marker_pos then
        reaper.ShowMessageBox("没有找到'X秒未对轨分隔线'标记(ID:99999)", "错误", 0)
        return
    end
    
    -- 获取当前选中的轨道
    local selected_track = reaper.GetSelectedTrack(0, 0)
    if not selected_track then
        reaper.ShowMessageBox("请先选择一个轨道", "提示", 0)
        return
    end
    
    -- 获取轨道上的所有媒体项
    local item_count = reaper.CountTrackMediaItems(selected_track)
    local items_to_delete = {}
    
    for i = 0, item_count - 1 do
        local item = reaper.GetTrackMediaItem(selected_track, i)
        local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
        local item_end = item_pos + reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        
        -- 检查音频块是否在指定范围内：
        -- 1. 音频块结束位置在光标左边
        -- 2. 音频块开始位置在"X秒未对轨分隔线"标记右边
        if item_end <= cursor_pos and item_pos >= marker_pos then
            table.insert(items_to_delete, item)
        end
    end
    
    if #items_to_delete == 0 then
        reaper.ShowMessageBox("在指定范围内没有找到需要删除的音频块", "提示", 0)
        return
    end
    
    -- 删除符合条件的音频块
    local deleted_count = 0
    for i, item in ipairs(items_to_delete) do
        reaper.DeleteTrackMediaItem(selected_track, item)
        deleted_count = deleted_count + 1
    end
    
    if deleted_count > 0 then
        -- 更新项目显示
        reaper.UpdateArrange()
        reaper.UpdateTimeline()
        reaper.ShowMessageBox(string.format("已删除 %d 个音频块", deleted_count), "完成", 0)
    end
end

-- 开始撤销块
reaper.Undo_BeginBlock()

-- 预防UI刷新
reaper.PreventUIRefresh(1)

-- 执行主函数
main()

-- 恢复UI刷新
reaper.PreventUIRefresh(-1)

-- 结束撤销块
reaper.Undo_EndBlock("删除光标左边音频块", -1)