-- 区间时长调整脚本
-- 功能：调整工程中区间的最短时长
-- 作者：Mark

-- 获取用户输入的最短时长
function get_min_duration_from_user()
    local retval, user_input = reaper.GetUserInputs("区间时长调整", 1, "请输入区间最短时长（分钟）:", "7")
    if not retval then
        return nil -- 用户取消
    end
    
    local min_duration = tonumber(user_input)
    if not min_duration or min_duration <= 0 then
        reaper.ShowMessageBox("请输入有效的数字（大于0）", "错误", 0)
        return nil
    end
    
    return min_duration * 60 -- 转换为秒
end

-- 获取所有区间信息
function get_all_regions()
    local regions = {}
    local i = 0
    
    while true do
        local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(i)
        if retval == 0 then break end
        
        if isrgn then -- 如果是区间
            table.insert(regions, {
                index = i,
                start_pos = pos,
                end_pos = rgnend,
                duration = rgnend - pos,
                name = name,
                markrgnindexnumber = markrgnindexnumber
            })
        end
        
        i = i + 1
    end
    
    -- 按开始位置排序
    table.sort(regions, function(a, b) return a.start_pos < b.start_pos end)
    
    return regions
end

-- 获取指定时间范围内的所有音频项目
function get_items_in_range(start_time, end_time)
    local items = {}
    local num_items = reaper.CountMediaItems(0)
    
    for i = 0, num_items - 1 do
        local item = reaper.GetMediaItem(0, i)
        local item_start = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
        local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        local item_end = item_start + item_length
        
        -- 检查项目是否在指定范围内
        if item_start < end_time and item_end > start_time then
            table.insert(items, {
                item = item,
                start = item_start,
                length = item_length,
                end_pos = item_end
            })
        end
    end
    
    return items
end

-- 移动音频项目
function move_items(items, offset)
    if #items == 0 then return end
    
    -- 先清除所有选择
    reaper.SelectAllMediaItems(0, false)
    
    -- 选择要移动的音频项目
    for _, item_info in ipairs(items) do
        reaper.SetMediaItemSelected(item_info.item, true)
    end
    
    -- 移动音频项目
    for _, item_info in ipairs(items) do
        local new_pos = item_info.start + offset
        reaper.SetMediaItemInfo_Value(item_info.item, "D_POSITION", new_pos)
    end
    
    -- 更新界面
    reaper.UpdateArrange()
    
    -- 清除选择
    reaper.SelectAllMediaItems(0, false)
end

-- 调整区间时长
function adjust_region_duration(region_index, new_end_pos)
    local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(region_index)
    if retval > 0 and isrgn then
        reaper.SetProjectMarker(markrgnindexnumber, true, pos, new_end_pos, name)
        return true
    end
    return false
end

-- 查找下一个时间不足的区间
function find_next_insufficient_region(regions, start_index, min_duration)
    for i = start_index, #regions do
        if regions[i].duration < min_duration then
            return i
        end
    end
    return nil
end

-- 计算区间之间的可移动音频
function get_movable_audio_between_regions(region1_end, region2_start, needed_duration)
    local items = get_items_in_range(region1_end, region2_start)
    if #items == 0 then
        return {}, 0
    end
    
    -- 按开始时间排序
    table.sort(items, function(a, b) return a.start < b.start end)
    
    local movable_items = {}
    local total_duration = 0
    
    for _, item_info in ipairs(items) do
        table.insert(movable_items, item_info)
        total_duration = item_info.end_pos - region1_end
        
        -- 如果已经足够了就停止
        if total_duration >= needed_duration then
            break
        end
    end
    
    return movable_items, total_duration
end

-- 获取完整音频块（确保不切断音频）
function get_complete_audio_blocks(start_time, end_time)
    local items = {}
    local num_items = reaper.CountMediaItems(0)
    
    for i = 0, num_items - 1 do
        local item = reaper.GetMediaItem(0, i)
        local item_start = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
        local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        local item_end = item_start + item_length
        
        -- 只选择完全在范围内的音频块（不切断音频）
        if item_start >= start_time and item_end <= end_time then
            table.insert(items, {
                item = item,
                start = item_start,
                length = item_length,
                end_pos = item_end
            })
        end
    end
    
    -- 按开始时间排序
    table.sort(items, function(a, b) return a.start < b.start end)
    
    return items
end

-- 计算完整音频块的总时长
function calculate_audio_duration(items)
    if #items == 0 then return 0 end
    
    local first_start = items[1].start
    local last_end = items[1].end_pos
    
    for _, item in ipairs(items) do
        if item.start < first_start then first_start = item.start end
        if item.end_pos > last_end then last_end = item.end_pos end
    end
    
    return last_end - first_start
end

-- 主要处理函数（重新设计的逻辑）
function process_regions(min_duration)
    local regions = get_all_regions()
    
    if #regions == 0 then
        reaper.ShowMessageBox("工程中没有找到区间", "提示", 0)
        return
    end
    
    reaper.Undo_BeginBlock()
    
    -- 确保项目状态是最新的
    reaper.UpdateArrange()
    
    local adjusted_count = 0
    local audio_gap = 1.0 -- 音频块之间的1秒间距
    local region_gap = 60.0 -- 区间与区间之间的60秒间隔
    
    reaper.ShowConsoleMsg("=== 开始分析区间 ===\n")
    
    -- 第一步：分析每个区间的实际情况
    for i = 1, #regions do
        local region = regions[i]
        local region_name = region.name or ("区间" .. i)
        
        reaper.ShowConsoleMsg(string.format("区间 %d (%s): 时长 %.2f 秒 (%.2f 分钟)\n", 
            i, region_name, region.duration, region.duration / 60))
        
        if region.duration < min_duration then
            local needed = min_duration - region.duration
            reaper.ShowConsoleMsg(string.format("  -> 需要补足 %.2f 秒\n", needed))
        else
            reaper.ShowConsoleMsg("  -> 时长充足\n")
        end
    end
    
    reaper.ShowConsoleMsg("\n=== 开始调整区间 ===\n")
    
    -- 第二步：按顺序调整每个区间（重新设计的逻辑）
    local i = 1
    while i <= #regions do
        -- 重新获取当前区间的实际信息（可能已经被前面的调整影响）
        local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(regions[i].index)
        if retval > 0 and isrgn then
            regions[i].start_pos = pos
            regions[i].end_pos = rgnend
            regions[i].duration = rgnend - pos
        end
        
        local current_region = regions[i]
        local region_name = current_region.name or ("区间" .. i)
        
        if current_region.duration < min_duration then
            local needed_duration = min_duration - current_region.duration
            
            reaper.ShowConsoleMsg(string.format("处理区间 %d (%s)，当前时长 %.2f 秒，需要补足 %.2f 秒\n", 
                i, region_name, current_region.duration, needed_duration))
            
            -- 只能从下一个区间获取音频
            if i < #regions then
                -- 重新获取下一个区间的实际信息
                local retval2, isrgn2, pos2, rgnend2, name2, markrgnindexnumber2 = reaper.EnumProjectMarkers(regions[i + 1].index)
                if retval2 > 0 and isrgn2 then
                    regions[i + 1].start_pos = pos2
                    regions[i + 1].end_pos = rgnend2
                    regions[i + 1].duration = rgnend2 - pos2
                end
                
                local next_region = regions[i + 1]
                
                -- 查找下一个区间前面的完整音频块（大约需要的时长）
                local search_end = math.min(next_region.start_pos + needed_duration * 1.5, next_region.end_pos)
                local available_items = get_complete_audio_blocks(next_region.start_pos, search_end)
                
                if #available_items > 0 then
                    -- 选择能满足需求的音频块
                    local selected_items = {}
                    local selected_duration = 0
                    
                    for _, item in ipairs(available_items) do
                        table.insert(selected_items, item)
                        selected_duration = calculate_audio_duration(selected_items)
                        
                        -- 如果已经足够就停止
                        if selected_duration >= needed_duration then
                            break
                        end
                    end
                    
                    if selected_duration > 0 then
                        -- 记录移动前的音频位置信息
                        local first_item_start = selected_items[1].start
                        local last_item_end = selected_items[#selected_items].end_pos
                        
                        -- 计算移动目标位置（当前区间最后一个音频块后1秒）
                        local current_region_items = get_items_in_range(current_region.start_pos, current_region.end_pos)
                        local target_pos = current_region.end_pos + audio_gap
                        if #current_region_items > 0 then
                            -- 找到当前区间最后一个音频块的结束位置
                            local last_audio_end = 0
                            for _, item in ipairs(current_region_items) do
                                if item.end_pos > last_audio_end then
                                    last_audio_end = item.end_pos
                                end
                            end
                            target_pos = last_audio_end + audio_gap
                        end
                        
                        local move_offset = target_pos - first_item_start
                        
                        reaper.ShowConsoleMsg(string.format("    选中 %d 个音频块，总时长 %.2f 秒\n", 
                            #selected_items, selected_duration))
                        reaper.ShowConsoleMsg(string.format("    移动音频：从 %.2f 到 %.2f，偏移 %.2f 秒\n", 
                            first_item_start, target_pos, move_offset))
                        
                        -- 移动选中的音频块到当前区间末尾
                        reaper.ShowConsoleMsg("    正在移动选中的音频块...\n")
                        move_items(selected_items, move_offset)
                        
                        -- 移动后续所有音频（从原来被移动音频的结束位置开始）
                        local remaining_items = get_items_in_range(last_item_end, math.huge)
                        reaper.ShowConsoleMsg(string.format("    移动后续音频：从 %.2f 开始的 %d 个音频块\n", 
                            last_item_end, #remaining_items))
                        if #remaining_items > 0 then
                            reaper.ShowConsoleMsg("    正在移动后续音频块...\n")
                            move_items(remaining_items, move_offset)
                        end
                        
                        -- 扩展当前区间到包含新移动的音频
                        local new_current_end = target_pos + selected_duration
                        adjust_region_duration(current_region.index, new_current_end)
                        current_region.end_pos = new_current_end
                        current_region.duration = new_current_end - current_region.start_pos
                        
                        -- 调整下一个区间的开始位置（它失去了一些音频）
                        local new_next_start = last_item_end + move_offset
                        
                        -- 重新计算下一个区间的时长
                        local new_next_duration = next_region.end_pos + move_offset - new_next_start
                        
                        -- 更新下一个区间
                        reaper.SetProjectMarker(markrgnindexnumber2, true, new_next_start, next_region.end_pos + move_offset, name2)
                        next_region.start_pos = new_next_start
                        next_region.end_pos = next_region.end_pos + move_offset
                        next_region.duration = new_next_duration
                        
                        -- 更新后续所有区间的位置
                        for j = i + 2, #regions do
                            local region_to_update = regions[j]
                            region_to_update.start_pos = region_to_update.start_pos + move_offset
                            region_to_update.end_pos = region_to_update.end_pos + move_offset
                            
                            local retval3, isrgn3, pos3, rgnend3, name3, markrgnindexnumber3 = reaper.EnumProjectMarkers(region_to_update.index)
                            if retval3 > 0 and isrgn3 then
                                reaper.SetProjectMarker(markrgnindexnumber3, true, 
                                    region_to_update.start_pos, region_to_update.end_pos, name3)
                            end
                        end
                        
                        adjusted_count = adjusted_count + 1
                        
                        reaper.ShowConsoleMsg(string.format("    区间 %d 调整完成，新时长：%.2f 秒\n", 
                            i, current_region.duration))
                        reaper.ShowConsoleMsg(string.format("    下一个区间 %d 新时长：%.2f 秒\n", 
                            i + 1, next_region.duration))
                        
                        -- 不要递增i，重新检查当前区间是否还需要调整
                        -- 因为可能还没有达到最小时长要求
                        
                    else
                        reaper.ShowConsoleMsg(string.format("    区间 %d 无法找到足够的音频块\n", i))
                        i = i + 1
                    end
                else
                    reaper.ShowConsoleMsg(string.format("    区间 %d 后面没有可用的音频块\n", i))
                    i = i + 1
                end
            else
                reaper.ShowConsoleMsg(string.format("    区间 %d 是最后一个区间，无法调整\n", i))
                i = i + 1
            end
        else
            reaper.ShowConsoleMsg(string.format("区间 %d (%s) 时长充足（%.2f 秒），跳过\n", i, region_name, current_region.duration))
            i = i + 1
        end
    end
    
    reaper.ShowConsoleMsg("\n=== 调整区间间隔 ===\n")
    
    -- 第三步：确保区间之间有60秒间隔
    regions = get_all_regions() -- 重新获取更新后的区间信息
    
    for i = 2, #regions do -- 从第二个区间开始
        local current_region = regions[i]
        local prev_region = regions[i - 1]
        
        local expected_start = prev_region.end_pos + region_gap
        local current_gap = current_region.start_pos - prev_region.end_pos
        
        if math.abs(current_gap - region_gap) > 0.1 then
            local offset = expected_start - current_region.start_pos
            
            -- 移动当前区间及后续所有内容
            local items_to_move = get_items_in_range(current_region.start_pos, math.huge)
            move_items(items_to_move, offset)
            
            -- 更新当前及后续区间位置
            for j = i, #regions do
                local region_to_adjust = regions[j]
                region_to_adjust.start_pos = region_to_adjust.start_pos + offset
                region_to_adjust.end_pos = region_to_adjust.end_pos + offset
                
                local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(region_to_adjust.index)
                if retval > 0 and isrgn then
                    reaper.SetProjectMarker(markrgnindexnumber, true, 
                        region_to_adjust.start_pos, region_to_adjust.end_pos, name)
                end
            end
            
            reaper.ShowConsoleMsg(string.format("调整区间 %d 间隔，偏移 %.2f 秒\n", i, offset))
        end
    end
    
    -- 最终更新界面
    reaper.UpdateArrange()
    reaper.TrackList_AdjustWindows(false)
    
    reaper.Undo_EndBlock("调整区间时长", -1)
    
    -- 显示完成信息
    local message = string.format("处理完成！\n\n调整了 %d 个区间\n区间间隔已设置为60秒", adjusted_count)
    reaper.ShowMessageBox(message, "区间时长调整完成", 0)
end

-- 主函数
function main()
    -- 检查是否有活动工程
    local project = reaper.EnumProjects(-1, "")
    if not project then
        reaper.ShowMessageBox("请先打开一个工程", "错误", 0)
        return
    end
    
    -- 获取用户输入的最短时长
    local min_duration = get_min_duration_from_user()
    if not min_duration then
        return -- 用户取消或输入无效
    end
    
    reaper.ShowConsoleMsg(string.format("开始处理，最短时长设置为 %.1f 分钟 (%.1f 秒)\n", 
        min_duration / 60, min_duration))
    
    -- 处理区间
    process_regions(min_duration)
end

-- 运行主函数
main()