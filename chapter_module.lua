-- 章节模块 - 处理章节识别和管理相关的逻辑
local r = reaper

-- 创建模块表
local chapter_module = {}

-- 章节数据结构
chapter_module.chapters = {}

-- 识别文本中的章节标题
-- 支持多种常见的章节标题格式
function chapter_module.extract_chapters(sentences)
  local chapters = {}
  
  -- 清空当前章节列表
  chapter_module.chapters = {}
  
  -- 遍历所有句子，查找章节标题
  for idx, sentence in ipairs(sentences) do
    -- 首先检查是否包含章节标记，如果有则提取内容
    local clean_sentence = sentence
    local chapter_content = sentence:match("%[CHAPTER%](.-)%[/CHAPTER%]")
    if chapter_content then
      clean_sentence = chapter_content
    end
    
    -- 匹配常见的章节标题格式
    -- 1. 第X章 格式
    local chapter_num = clean_sentence:match("^%s*第([%d一二三四五六七八九十百千]+)章")
    -- 2. 第X章：章节名 格式
    local chapter_num_with_title = clean_sentence:match("^%s*第([%d一二三四五六七八九十百千]+)章[：:](.+)")
    -- 3. 章节X 格式
    local chapter_alt = clean_sentence:match("^%s*章节(%d+)")
    -- 4. 第X节 格式
    local section_num = clean_sentence:match("^%s*第([%d一二三四五六七八九十百千]+)节")
    -- 5. 第X集 格式
    local episode_num = clean_sentence:match("^%s*第([%d一二三四五六七八九十百千]+)集")
    -- 6. 第X话 格式
    local story_num = clean_sentence:match("^%s*第([%d一二三四五六七八九十百千]+)话")
    -- 7. 第X回 格式
    local round_num = clean_sentence:match("^%s*第([%d一二三四五六七八九十百千]+)回")
    
    if chapter_num or chapter_num_with_title or chapter_alt or section_num or episode_num or story_num or round_num then
      local num = chapter_num or (chapter_num_with_title and chapter_num_with_title) or chapter_alt or section_num or episode_num or story_num or round_num
      local title = clean_sentence  -- 使用清理后的文本作为标题
      
      -- 将中文数字转换为阿拉伯数字
      if num:match("[一二三四五六七八九十百千]") then
        num = chapter_module.chinese_to_number(num)
      else
        num = tonumber(num) or 0
      end
      
      -- 添加到章节列表
      table.insert(chapters, {
        index = idx,       -- 在原文中的索引位置
        number = num,      -- 章节编号
        title = title,     -- 完整标题
        sentence_idx = idx -- 对应的句子索引
      })
    end
  end
  
  -- 按章节编号排序
  table.sort(chapters, function(a, b) return a.number < b.number end)
  
  -- 保存章节列表
  chapter_module.chapters = chapters
  
  return chapters
end

-- 将中文数字转换为阿拉伯数字
function chapter_module.chinese_to_number(chinese_num)
  local values = {
    ["一"] = 1, ["二"] = 2, ["三"] = 3, ["四"] = 4, ["五"] = 5,
    ["六"] = 6, ["七"] = 7, ["八"] = 8, ["九"] = 9, ["十"] = 10,
    ["百"] = 100, ["千"] = 1000
  }
  
  -- 简单情况：单个数字
  if values[chinese_num] then
    return values[chinese_num]
  end
  
  -- 复杂情况：需要计算
  local result = 0
  local temp = 0
  local mult = 1
  
  for i = 1, #chinese_num do
    local char = chinese_num:sub(i, i)
    local val = values[char]
    
    if val then
      if val >= 10 then  -- 是单位（十、百、千）
        if temp == 0 then temp = 1 end
        result = result + temp * val
        temp = 0
        mult = val / 10  -- 更新下一个数字的单位
      else  -- 是数字（一至九）
        temp = val
      end
    end
  end
  
  -- 加上最后一个数字
  if temp > 0 then
    result = result + temp * mult
  end
  
  return result > 0 and result or 1  -- 确保至少返回1
end

-- 获取章节列表
function chapter_module.get_chapters()
  return chapter_module.chapters
end

-- 跳转到指定章节
function chapter_module.jump_to_chapter(chapter_idx, scroll_callback)
  if not chapter_module.chapters or #chapter_module.chapters == 0 then
    return false, "没有可用的章节"
  end
  
  if chapter_idx < 1 or chapter_idx > #chapter_module.chapters then
    return false, "章节索引超出范围"
  end
  
  local target_sentence_idx = chapter_module.chapters[chapter_idx].sentence_idx
  
  -- 调用回调函数执行滚动
  if scroll_callback and type(scroll_callback) == "function" then
    scroll_callback(target_sentence_idx)
    return true, "已跳转到章节: " .. chapter_module.chapters[chapter_idx].title
  end
  
  return false, "滚动回调函数未提供"
end

-- 根据句子索引获取所属章节
function chapter_module.get_chapter_by_sentence_idx(sentence_idx)
  local chapters = chapter_module.chapters
  if #chapters == 0 then return nil end
  
  -- 遍历章节列表，找到句子所属的章节
  for i = #chapters, 1, -1 do
    if sentence_idx >= chapters[i].sentence_idx then
      return chapters[i], i
    end
  end
  
  -- 如果句子在第一章之前，返回第一章
  return chapters[1], 1
end

-- 返回模块
return chapter_module