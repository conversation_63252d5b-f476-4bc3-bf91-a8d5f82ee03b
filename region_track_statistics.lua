-- REAPER Script: Region and Track Statistics
-- 功能：统计区间数量并计算区间和轨道的总时长

-- 开始撤销块
reaper.Undo_BeginBlock()

-- 获取当前工程
local project = 0

-- 区间统计
local total_region_time = 0
local region_count = 0
local regions = {}  -- 存储所有区间信息

-- 获取工程中的所有标记和区间
local num_markers, num_regions = reaper.CountProjectMarkers(project)
local total_markers_regions = num_markers + num_regions

-- 枚举所有标记和区间，筛选出区间
for i = 0, total_markers_regions - 1 do
    local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers2(project, i, false)
    if retval and isrgn then  -- isrgn为true表示这是一个区间
        region_count = region_count + 1
        local region_duration = rgnend - pos
        total_region_time = total_region_time + region_duration
        
        -- 保存区间信息供轨道统计使用
        table.insert(regions, {
            start_pos = pos,
            end_pos = rgnend,
            name = name
        })
    end
end

-- 计算两个时间段的交集长度
local function get_intersection_duration(start1, end1, start2, end2)
    local intersection_start = math.max(start1, start2)
    local intersection_end = math.min(end1, end2)
    if intersection_start < intersection_end then
        return intersection_end - intersection_start
    else
        return 0
    end
end

-- 轨道统计
local track_count = reaper.CountTracks(project)
local tracks_info = {}
local total_track_time = 0

for i = 0, track_count - 1 do
    local track = reaper.GetTrack(project, i)
    local retval, track_name = reaper.GetSetMediaTrackInfo_String(track, "P_NAME", "", false)
    
    if track_name == "" then
        track_name = "轨道 " .. (i + 1)
    end
    
    local track_duration_in_regions = 0
    local item_count = reaper.CountTrackMediaItems(track)
    
    -- 计算轨道中媒体项在区间内的总时长
    for j = 0, item_count - 1 do
        local item = reaper.GetTrackMediaItem(track, j)
        local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
        local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        local item_end = item_pos + item_length
        
        -- 计算该媒体项与所有区间的交集时长
        for k, region in ipairs(regions) do
            local intersection = get_intersection_duration(item_pos, item_end, region.start_pos, region.end_pos)
            track_duration_in_regions = track_duration_in_regions + intersection
        end
    end
    
    table.insert(tracks_info, {
        name = track_name,
        duration = track_duration_in_regions
    })
    
    if track_duration_in_regions > total_track_time then
        total_track_time = track_duration_in_regions
    end
end

-- 时间格式化函数
local function format_time(seconds)
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    if hours > 0 then
        return string.format("%02d:%02d:%06.3f", hours, minutes, secs)
    else
        return string.format("%02d:%06.3f", minutes, secs)
    end
end

-- 获取工程名称
local project_name = "未命名工程"

-- 使用EnumProjects获取当前工程和文件名
local current_project, proj_filename = reaper.EnumProjects(-1, "")
if proj_filename and proj_filename ~= "" then
    -- 从完整路径中提取文件名（不含扩展名）
    local filename = proj_filename:match("([^\\]+)%.rpp$") or proj_filename:match("([^/]+)%.rpp$")
    if filename then
        project_name = filename
    else
        -- 如果没有.rpp扩展名，提取最后的文件名部分
        filename = proj_filename:match("([^\\]+)$") or proj_filename:match("([^/]+)$")
        if filename and filename ~= "" then
            project_name = filename
        end
    end
else
    -- 备选方案：尝试其他方法
    if current_project then
        local retval, proj_name = reaper.GetProjectName(current_project, "")
        if proj_name and proj_name ~= "" then
            project_name = proj_name
        end
    end
end

-- 构建统计报告
local report = "工程名称: " .. project_name .. "\n"
report = report .. "=== REAPER 工程统计 ===\n\n"

-- 区间统计
report = report .. "[区间统计]\n"
report = report .. "区间总数: " .. region_count .. "\n"
report = report .. "区间总时长: " .. format_time(total_region_time) .. "\n\n"

-- 轨道统计
report = report .. "[轨道统计]\n"
report = report .. "轨道总数: " .. track_count .. "\n"

if track_count > 0 then
    report = report .. "轨道详情:\n"
    for i, track in ipairs(tracks_info) do
        report = report .. string.format("  %s: %s\n", 
            track.name, format_time(track.duration))
    end
    report = report .. "\n"
end

-- 汇总
report = report .. "[汇总]\n"
report = report .. "区间总数: " .. region_count .. "\n"
report = report .. "轨道总数: " .. track_count .. "\n"
report = report .. "区间总时长: " .. format_time(total_region_time) .. "\n"

-- 显示统计报告
reaper.ShowMessageBox(report, "REAPER 工程统计", 0)

-- 结束撤销块
reaper.Undo_EndBlock("区间和轨道统计", -1)