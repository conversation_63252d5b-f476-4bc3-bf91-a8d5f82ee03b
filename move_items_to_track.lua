-- 移动选中音频块到指定轨道
-- 作者: ST脚本工具
-- 功能: 将选中的音频块或多个音频块垂直移动到用户指定的轨道上

local r = reaper

-- 检查是否有选中的音频块
function check_selected_items()
    local item_count = r.CountSelectedMediaItems(0)
    if item_count == 0 then
        r.ShowMessageBox("请先选择要移动的音频块！", "提示", 0)
        return false
    end
    return true, item_count
end

-- 获取用户输入的轨道编号
function get_target_track_number()
    local track_count = r.CountTracks(0)
    local retval, user_input = r.GetUserInputs("移动音频块到指定轨道", 1, 
        "目标轨道编号 (1-" .. track_count .. "):", "1")
    
    if not retval then
        return nil -- 用户取消了操作
    end
    
    local track_number = tonumber(user_input)
    if not track_number then
        r.ShowMessageBox("请输入有效的数字！", "错误", 0)
        return nil
    end
    
    if track_number < 1 or track_number > track_count then
        r.ShowMessageBox("轨道编号超出范围！\n当前项目有 " .. track_count .. " 条轨道。", "错误", 0)
        return nil
    end
    
    return track_number
end

-- 移动音频块到指定轨道
function move_items_to_track(target_track_number)
    local target_track = r.GetTrack(0, target_track_number - 1) -- REAPER轨道索引从0开始
    if not target_track then
        r.ShowMessageBox("无法获取目标轨道！", "错误", 0)
        return false
    end
    
    local item_count = r.CountSelectedMediaItems(0)
    local moved_count = 0
    
    -- 先收集所有选中的音频块到一个表中，避免移动过程中索引变化
    local selected_items = {}
    for i = 0, item_count - 1 do
        local item = r.GetSelectedMediaItem(0, i)
        if item then
            table.insert(selected_items, item)
        end
    end
    
    -- 开始撤销块
    r.Undo_BeginBlock()
    
    -- 遍历收集到的音频块进行移动
    for i, item in ipairs(selected_items) do
        if item and r.ValidatePtr2(0, item, "MediaItem*") then -- 验证项目是否仍然有效
            -- 移动音频块到目标轨道
            local success = r.MoveMediaItemToTrack(item, target_track)
            if success then
                moved_count = moved_count + 1
            end
        end
    end
    
    -- 更新界面
    r.UpdateArrange()
    
    -- 结束撤销块
    r.Undo_EndBlock("移动 " .. moved_count .. " 个音频块到轨道 " .. target_track_number, -1)
    
    return moved_count
end

-- 主函数
function main()
    -- 检查是否有选中的音频块
    local has_items, item_count = check_selected_items()
    if not has_items then
        return
    end
    
    -- 获取目标轨道编号
    local target_track_number = get_target_track_number()
    if not target_track_number then
        return
    end
    
    -- 移动音频块
    local moved_count = move_items_to_track(target_track_number)
end

-- 运行主函数
main()