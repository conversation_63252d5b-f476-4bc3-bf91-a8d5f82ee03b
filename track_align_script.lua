-- 对轨脚本：根据CV信息自动选择对应的轨道
-- 此脚本由mark.lua调用，通过全局变量获取CV和角色信息

-- 获取传入的CV和角色信息
local selected_cv = _G.selected_cv_for_track_align or ""
local selected_role = _G.selected_role_for_track_align or ""
local is_right_click = _G.is_right_click_for_track_align or false

-- 清理全局变量
_G.selected_cv_for_track_align = nil
_G.selected_role_for_track_align = nil
_G.is_right_click_for_track_align = nil

-- 检查REAPER是否可用
if not reaper then
  return
end

-- CV到轨道的映射表（可根据实际需要修改）
local cv_to_track_map = {
  ["旁白"] = 1,
  ["角色A"] = 2,
  ["角色B"] = 3,
  ["角色C"] = 4,
  ["角色D"] = 5,
  ["角色E"] = 6,
  ["角色F"] = 7,
  ["角色G"] = 8,
  ["角色H"] = 9,
  -- 可以继续添加更多CV到轨道的映射
}

-- 主要处理函数
function align_track_by_cv(cv_name)
  if not cv_name or cv_name == "" then
    return false
  end
  
  -- 查找对应的轨道编号
  local track_number = cv_to_track_map[cv_name]
  
  if not track_number then
    -- 如果映射表中没有找到，尝试通过轨道名称精确匹配
    local track_count = reaper.CountTracks(0)
    for i = 0, track_count - 1 do
      local track = reaper.GetTrack(0, i)
      if track then
        local retval, track_name = reaper.GetSetMediaTrackInfo_String(track, "P_NAME", "", false)
        if retval and track_name and track_name ~= "" then
          -- 检查轨道名称是否与CV名称完全匹配
          if track_name == cv_name then
            track_number = i + 1  -- 轨道编号从1开始
            break
          end
        end
      end
    end
  end
  
  if not track_number then
    -- 如果还是没有找到，尝试使用CV名称的数字部分
    local number = cv_name:match("%d+")
    if number then
      track_number = tonumber(number)
    end
  end
  
  if not track_number then
    return false
  end
  
  -- 获取项目中的轨道总数
  local track_count = reaper.CountTracks(0)
  
  if track_number > track_count then
    return false
  end
  
  -- 获取目标轨道（轨道编号从0开始）
  local target_track = reaper.GetTrack(0, track_number - 1)
  
  if not target_track then
    return false
  end
  
  -- 取消所有轨道的选择
  reaper.Main_OnCommand(40297, 0) -- Track: Unselect all tracks
  
  -- 选择目标轨道
  reaper.SetTrackSelected(target_track, true)
  
  -- 将轨道滚动到可见区域
  reaper.SetMixerScroll(target_track)
  
  -- 更新界面
  reaper.UpdateArrange()
  reaper.TrackList_AdjustWindows(false)
  
  -- 已选择轨道
  
  -- 根据操作类型执行不同的FengYi脚本
  -- 获取当前脚本路径
  local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
  local script_dir = script_path:match("(.+[\\/])") or "./"
  
  local fengyiScript
  if is_right_click then
    -- 右键执行empty_media脚本
    fengyiScript = script_dir .. "../FengYiScripts/FengYi_to_the_rail_assistant_pure_shortcut_key_version_empty_media.lua"
  else
    -- 左键执行普通脚本
    fengyiScript = script_dir .. "../FengYiScripts/FengYi_to_the_rail_assistant_pure_shortcut_key_version.lua"
  end
  
  local file = io.open(fengyiScript, "r")
  if file then
    file:close()
    dofile(fengyiScript)
  end
  
  return true
end

-- 执行对轨操作
if selected_cv and selected_cv ~= "" then
  align_track_by_cv(selected_cv)
end