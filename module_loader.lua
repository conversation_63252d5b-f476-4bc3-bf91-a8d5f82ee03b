-- 模块加载器 - 统一管理所有模块的导入和依赖关系
-- 此模块解决循环依赖问题，并提供统一的错误处理

local r = reaper  -- REAPER API引用

-- 创建模块加载器
local module_loader = {}

-- 存储已加载的模块
local loaded_modules = {}

-- 模块加载顺序配置，更新添加cache_module
local module_load_order = {
  "utils_module",      -- 1. 工具模块（最基础的功能）
  "cache_module",      -- 2. 缓存模块（新添加）
  "style_module",      -- 3. 样式模块（UI相关）
  "playback_control",  -- 4. 播放控制
  "text_utils",        -- 5. 文本处理
  "chapter_module",    -- 6. 章节管理
  "excel_module",      -- 7. Excel处理
  "data_persist_module", -- 8. 数据持久化模块
  "word_module",       -- 9. Word文档处理模块
  -- "mark_function" 已合并到 button_module
  "button_module",     -- 11. 按钮模块（依赖多个其他模块）
  "button_helpers"     -- 12. 按钮辅助模块（处理按钮额外功能）
}

-- 模块依赖配置，更新依赖关系
local module_dependencies = {
  style_module = {"utils_module"},
  text_utils = {"utils_module"},
  button_module = {"utils_module", "style_module", "word_module", "text_utils"}, -- 添加 text_utils 依赖，因为 mark_item 需要 format_time
  word_module = {"utils_module"} -- 添加word_module的依赖
  -- cache_module和playback_control已合并到utils_module中
  -- mark_function 的依赖已合并到 button_module
}

-- 模块映射配置，用于处理合并后的模块
local module_mapping = {
  button_helpers = "style_module", -- button_helpers已合并到style_module中
  mark_function = "button_module", -- mark_function已合并到button_module中 (确认映射正确)
  data_persist_module = "excel_module", -- data_persist_module已合并到excel_module中
  playback_control = "utils_module", -- playback_control已合并到utils_module中
  cache_module = "utils_module" -- cache_module已合并到utils_module中
}

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载文件的函数
-- @param module_name 模块名称（不含.lua扩展名）
-- @param force_reload 是否强制重新加载
-- @param dependencies 该模块的依赖模块
-- @param config 全局配置参数
-- @return table 加载的模块或空表（如果加载失败）
function module_loader.load(module_name, force_reload, dependencies, config)
  -- 检查是否需要映射到其他模块
  local actual_module_name = module_mapping[module_name] or module_name

  -- 如果是映射的模块且已加载且不需要强制重新加载，则直接返回
  if not force_reload then
    if loaded_modules[module_name] then
      return loaded_modules[module_name]
    elseif module_mapping[module_name] and loaded_modules[actual_module_name] then
      -- 如果是映射的模块，且目标模块已加载，则缓存并返回
      loaded_modules[module_name] = loaded_modules[actual_module_name]
      return loaded_modules[module_name]
    end
  end

  -- 使用utils_module来安全加载文件（如果已加载）
  local file_path = script_dir .. actual_module_name .. ".lua"
  local result

  -- 检查utils_module是否已加载，是则使用其安全加载函数
  if loaded_modules["utils_module"] and loaded_modules["utils_module"].safe_dofile then
    result = loaded_modules["utils_module"].safe_dofile(file_path, {})
  else
    -- 否则使用pcall直接加载
    local success, loaded_result = pcall(dofile, file_path)
    if not success then
      -- 记录错误并返回空表
      r.ShowConsoleMsg("加载模块失败: " .. actual_module_name .. ", 错误: " .. tostring(loaded_result) .. "\n")
      return {}
    end
    result = loaded_result
  end

  -- 存储已加载的模块
  loaded_modules[actual_module_name] = result

  -- 如果是映射的模块，也缓存原始模块名
  if module_mapping[module_name] then
    loaded_modules[module_name] = result
  end

  -- 如果模块有init函数且提供了依赖，则调用初始化
  if result.init and type(result.init) == "function" then
    -- 过滤仅传递该模块所需的依赖
    local module_deps = {}
    if dependencies and module_dependencies[actual_module_name] then
      for _, required_dep_name in ipairs(module_dependencies[actual_module_name]) do
        if dependencies[required_dep_name] then
          module_deps[required_dep_name] = dependencies[required_dep_name]
        end
      end
    end

    -- 调用模块初始化函数
    local initialized_module = result.init(module_deps, config)
    if initialized_module then
      loaded_modules[actual_module_name] = initialized_module
      -- 如果是映射的模块，也更新原始模块名的缓存
      if module_mapping[module_name] then
        loaded_modules[module_name] = initialized_module
      end
      return initialized_module
    end
  end

  return result
end

-- 加载所有核心模块的函数
-- @param config 可选的配置参数
-- @return table 所有已加载模块的表
function module_loader.load_all(config)
  config = config or {}
  local modules = {}
  local dependencies = {}

  -- 按照预定义顺序加载模块
  for _, module_name in ipairs(module_load_order) do
    -- 跳过配置中禁用的模块
    if not (config.disabled_modules and config.disabled_modules[module_name]) then
      -- 将全局配置传递给load函数
      local module = module_loader.load(module_name, config.force_reload, dependencies, config)
      modules[module_name] = module
      dependencies[module_name] = module
    end
  end

  -- 检查模块初始化状态
  local all_initialized = true
  local failed_modules = {}

  for module_name, module in pairs(modules) do
    if type(module) ~= "table" or next(module) == nil then
      all_initialized = false
      table.insert(failed_modules, module_name)
    end
  end

  -- 如果有模块初始化失败，记录日志
  if not all_initialized and #failed_modules > 0 then
    local error_msg = "以下模块初始化失败: " .. table.concat(failed_modules, ", ")
    r.ShowConsoleMsg(error_msg .. "\n")
  end

  -- 设置全局共享状态（如果配置中启用）
  if config.use_global_state then
    module_loader.modules = modules
  end

  return modules
end

-- 获取当前已加载的所有模块
function module_loader.get_loaded_modules()
  return loaded_modules
end

-- 获取指定模块
function module_loader.get_module(module_name)
  return loaded_modules[module_name]
end

-- 重置模块缓存
function module_loader.reset()
  loaded_modules = {}
end

-- 重新加载指定模块及其依赖
function module_loader.reload_module(module_name, config)
  -- 如果指定的模块不存在，直接返回
  if not loaded_modules[module_name] then
    return false, "模块未加载: " .. module_name
  end

  -- 先重置该模块
  loaded_modules[module_name] = nil

  -- 重新加载该模块
  local dependencies = {}
  for dep_name, dep_module in pairs(loaded_modules) do
    dependencies[dep_name] = dep_module
  end

  -- 强制重新加载，传递配置
  local reloaded_module = module_loader.load(module_name, true, dependencies, config)

  return type(reloaded_module) == "table" and next(reloaded_module) ~= nil, reloaded_module
end

-- 添加帮助函数：获取脚本目录
function module_loader.get_script_dir()
  return script_dir
end

-- 返回模块加载器
return module_loader