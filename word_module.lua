--[[
 * ReaScript 名称: Word模块
 * 描述: 提取docx文档中的文本并保留颜色和删除线样式
 * 作者: Augment Agent
 * 版本: 1.0
--]]

-- 创建一个模块
local WordModule = {}

-- 配置选项
WordModule.config = {
  -- 设置为true只会标记角色-CV格式，设置为false会标记所有高亮文本
  strict_role_cv_matching = false
}

-- 检查reaper API是否可用
local has_reaper = (reaper ~= nil)
local r = reaper -- 创建一个简短的引用

-- 处理文本，确保中文字符正确显示
local function ensure_utf8(text)
  -- 简单检查，确保不返回nil
  if text == nil then
    return ""
  end
  return tostring(text)
end

-- HTML实体解码函数
local function decode_html_entities(text)
  if not text then return "" end
  
  -- 解码常见的HTML实体
  text = text:gsub("&quot;", '"')
  text = text:gsub("&apos;", "'")
  text = text:gsub("&lt;", "<")
  text = text:gsub("&gt;", ">")
  text = text:gsub("&amp;", "&")  -- 这个要放在最后，避免重复解码
  
  return text
end

-- 十六进制颜色转换为RGB表（范围0-1）
function WordModule.hex_to_rgb(hex)
  -- 确保输入是字符串
  hex = tostring(hex or "")

  -- 移除可能的#前缀
  hex = hex:gsub("#", "")

  -- 确保是6位十六进制
  if #hex ~= 6 then
    return {r = 0, g = 0, b = 0, a = 1}  -- 默认黑色
  end

  -- 提取RGB分量并转换为0-1范围的数值
  local r = tonumber(hex:sub(1, 2), 16) / 255
  local g = tonumber(hex:sub(3, 4), 16) / 255
  local b = tonumber(hex:sub(5, 6), 16) / 255

  return {r = r, g = g, b = b, a = 1}
end

-- 解析docx文件的函数
function WordModule.parse_docx(file_path)
  -- 检查文件是否存在
  if not file_path then
    return "文件路径未指定", nil
  end

  local test_file = io.open(file_path, "rb")
  if not test_file then
    return "无法打开文件: " .. file_path, nil
  end
  test_file:close()

  -- 检查是否是docx文件
  if not file_path:lower():match("%.docx$") then
    return "文件不是.docx格式: " .. file_path, nil
  end

  -- 获取脚本路径
  local script_path = debug.getinfo(1,'S').source:match[[^@?(.*[\/])[^\/]-$]]
  if not script_path then script_path = "" end

  -- 创建data目录（如果不存在）
  local data_dir = script_path .. "data"
  local temp_dir = data_dir .. "\\temp"
  local extract_dir = temp_dir .. "\\extracted"
  local temp_docx = temp_dir .. "\\temp.docx"
  local temp_zip = temp_dir .. "\\temp.zip"
  local debug_dir = data_dir .. "\\debug"

  -- 创建debug目录用于排查问题
  if r.JS_Process_ExecuteCommand then
    r.JS_Process_ExecuteCommand('mkdir "' .. debug_dir .. '" 2>nul', false)
  else
    os.execute('mkdir "' .. debug_dir .. '" 2>nul')
  end

  -- 先同步创建data目录
  if r.JS_Process_ExecuteCommand then
    r.JS_Process_ExecuteCommand('mkdir "' .. data_dir .. '" 2>nul', false)
  else
    os.execute('mkdir "' .. data_dir .. '" 2>nul')
  end

  -- 确保data目录存在
  local test_file = io.open(data_dir .. "\\test.tmp", "w")
  if not test_file then
    return "无法创建data目录，请检查写入权限。"
  end
  test_file:close()
  os.remove(data_dir .. "\\test.tmp")

  -- 清理旧的临时目录（如果存在）
  if r.JS_Process_ExecuteCommand then
    r.JS_Process_ExecuteCommand('rmdir /S /Q "' .. temp_dir .. '" 2>nul', false)
  else
    os.execute('rmdir /S /Q "' .. temp_dir .. '" 2>nul')
  end

  -- 创建临时目录结构
  if r.JS_Process_ExecuteCommand then
    r.JS_Process_ExecuteCommand('mkdir "' .. temp_dir .. '" 2>nul', false)
    r.JS_Process_ExecuteCommand('mkdir "' .. extract_dir .. '" 2>nul', false)
  else
    os.execute('mkdir "' .. temp_dir .. '" 2>nul')
    os.execute('mkdir "' .. extract_dir .. '" 2>nul')
  end

  -- 确保临时目录可写
  test_file = io.open(temp_dir .. "\\test.tmp", "w")
  if not test_file then
    return "无法创建临时目录，请检查写入权限。"
  end
  test_file:close()
  os.remove(temp_dir .. "\\test.tmp")

  -- 使用二进制模式复制文件
  local src_file = io.open(file_path, "rb")
  if not src_file then
    return "无法打开源文件。文件可能不存在或无法访问。"
  end

  local dst_file = io.open(temp_docx, "wb")
  if not dst_file then
    src_file:close()
    return "无法创建临时文件。可能没有写入权限。"
  end

  -- 复制文件内容
  local content = src_file:read("*all")
  dst_file:write(content)
  src_file:close()
  dst_file:close()

  -- 将docx文件复制为zip文件
  src_file = io.open(temp_docx, "rb")
  if not src_file then
    return "无法打开临时docx文件。"
  end

  dst_file = io.open(temp_zip, "wb")
  if not dst_file then
    src_file:close()
    return "无法创建临时zip文件。"
  end

  -- 复制文件内容
  content = src_file:read("*all")
  dst_file:write(content)
  src_file:close()
  dst_file:close()

  -- 使用PowerShell解压文件
  if r.JS_Process_ExecuteCommand then
    -- 使用JS API执行PowerShell命令，确保完全隐藏窗口
    -- 创建一个临时的VBS脚本文件来隐藏执行PowerShell
    local vbs_script_path = temp_dir .. "\\run_hidden.vbs"
    local vbs_script = io.open(vbs_script_path, "w")
    if vbs_script then
      -- 创建PowerShell脚本
      local ps_script_path = temp_dir .. "\\extract_docx.ps1"
      local ps_script = io.open(ps_script_path, "w")
      if ps_script then
        -- 写入PowerShell脚本内容
        ps_script:write([[$ErrorActionPreference = "SilentlyContinue"
Add-Type -AssemblyName System.IO.Compression.FileSystem
try {
  [System.IO.Compression.ZipFile]::ExtractToDirectory(']] .. temp_zip:gsub('\\', '\\\\') .. [[', ']] .. extract_dir:gsub('\\', '\\\\') .. [[')
} catch {
  try {
    Expand-Archive -Path ']] .. temp_zip .. [[' -DestinationPath ']] .. extract_dir .. [[' -Force
  } catch {}
}]])
        ps_script:close()

        -- 写入VBS脚本内容，使用CreateObject("WScript.Shell").Run来完全隐藏执行
        vbs_script:write([[
Set objShell = CreateObject("WScript.Shell")
objShell.Run "powershell -NoProfile -NonInteractive -WindowStyle Hidden -ExecutionPolicy Bypass -File """ & "]] .. ps_script_path:gsub("\\", "\\\\") .. [[" & """", 0, True
]])
        vbs_script:close()

        -- 使用JS_ShellExecute执行VBS脚本，完全隐藏窗口
        -- 使用0作为显示参数（SW_HIDE）
        r.JS_ShellExecute(vbs_script_path, "", "", "open", 0)

        -- 等待一小段时间，确保脚本有时间执行
        r.defer(function() end)
      else
        -- 如果无法创建PowerShell脚本文件，使用直接命令
        -- 写入VBS脚本内容，使用CreateObject("WScript.Shell").Run来完全隐藏执行
        local ps_cmd = 'powershell -NoProfile -NonInteractive -WindowStyle Hidden -ExecutionPolicy Bypass -Command "& { Add-Type -AssemblyName System.IO.Compression.FileSystem; try { [System.IO.Compression.ZipFile]::ExtractToDirectory(\'' .. temp_zip:gsub('\\', '\\\\') .. '\', \'' .. extract_dir:gsub('\\', '\\\\') .. '\') } catch { try { Expand-Archive -Path \'' .. temp_zip .. '\' -DestinationPath \'' .. extract_dir .. '\' -Force } catch {} } }"'

        vbs_script:write([[
Set objShell = CreateObject("WScript.Shell")
objShell.Run "]] .. ps_cmd:gsub('"', '""') .. [[", 0, True
]])
        vbs_script:close()

        -- 使用JS_ShellExecute执行VBS脚本，完全隐藏窗口
        r.JS_ShellExecute(vbs_script_path, "", "", "open", 0)

        -- 等待一小段时间，确保脚本有时间执行
        r.defer(function() end)
      end
    else
      -- 如果无法创建VBS脚本文件，回退到直接使用JS_Process_ExecuteCommand
      local ps_cmd = 'powershell -NoProfile -NonInteractive -WindowStyle Hidden -ExecutionPolicy Bypass -Command "& { Add-Type -AssemblyName System.IO.Compression.FileSystem; try { [System.IO.Compression.ZipFile]::ExtractToDirectory(\'' .. temp_zip:gsub('\\', '\\\\') .. '\', \'' .. extract_dir:gsub('\\', '\\\\') .. '\') } catch { try { Expand-Archive -Path \'' .. temp_zip .. '\' -DestinationPath \'' .. extract_dir .. '\' -Force } catch {} } }"'
      r.JS_Process_ExecuteCommand(ps_cmd, false)
    end
  else
    -- 降级到标准os.execute
    local ps_cmd = 'powershell -NoProfile -NonInteractive -WindowStyle Hidden -ExecutionPolicy Bypass -Command "& { Add-Type -AssemblyName System.IO.Compression.FileSystem; try { [System.IO.Compression.ZipFile]::ExtractToDirectory(\'' .. temp_zip:gsub('\\', '\\\\') .. '\', \'' .. extract_dir:gsub('\\', '\\\\') .. '\') } catch { try { Expand-Archive -Path \'' .. temp_zip .. '\' -DestinationPath \'' .. extract_dir .. '\' -Force } catch {} } }"'
    os.execute(ps_cmd .. ' >nul 2>&1')
  end

  -- 检查document.xml文件
  local content_file = extract_dir .. "\\word\\document.xml"

  -- 读取文件内容
  local file = io.open(content_file, "r")
  if not file then
    -- 清理临时目录
    if r.JS_Process_ExecuteCommand then
      r.JS_Process_ExecuteCommand('rmdir /S /Q "' .. temp_dir .. '" 2>nul', false)
    else
      os.execute('rmdir /S /Q "' .. temp_dir .. '" 2>nul')
    end
    return "无法解压docx/zip文件。文件可能不是标准的Word文档格式或已损坏。"
  end

  -- 特殊处理中文字符编码
  local xml_content = file:read("*all")
  file:close()

  -- 将XML内容保存到调试文件
  local debug_file = io.open(debug_dir .. "\\document_xml.txt", "w")
  if debug_file then
    debug_file:write(xml_content)
    debug_file:close()
  end

  -- 提取段落
  local paragraphs = {}
  local p_count = 0
  local table_paragraph_count = 0  -- 表格段落计数
  local non_table_paragraph_count = 0  -- 非表格段落计数
  local highlight_count = 0
  local strike_count = 0
  local strike_regions = {}  -- 用于调试删除线区域

  -- 匹配段落
  for p_content in string.gmatch(xml_content, "<w:p[^>]*>(.-)</w:p>") do
    p_count = p_count + 1
    
    -- 检查段落样式，跳过表格内容
    local pPr = string.match(p_content, "<w:pPr>(.-)</w:pPr>")
    local is_table_content = false
    
    if pPr then
      -- 检查是否为表格内容样式
      if string.find(pPr, '<w:pStyle%s+w:val="TableContents"') or
         string.find(pPr, '<w:pStyle[^>]*w:val="TableContents"[^>]*>') or
         string.find(pPr, '<w:pStyle[^>]*w:val="TableContents"[^>]*/>')  then
        is_table_content = true
      end
    end
    
    -- 如果是表格内容，跳过此段落
    if is_table_content then
      table_paragraph_count = table_paragraph_count + 1
      -- 可选：记录跳过的表格段落用于调试
      -- print("跳过表格段落 #" .. p_count)
      goto continue
    end
    
    -- 非表格段落计数
    non_table_paragraph_count = non_table_paragraph_count + 1
    local paragraph = {runs = {}, id = p_count}  -- 添加段落ID用于跟踪

    -- 匹配段落中的文本运行
    for r_content in string.gmatch(p_content, "<w:r[^>]*>(.-)</w:r>") do
      local run = {text = "", format = {}}

      -- 提取格式信息
      local rPr = string.match(r_content, "<w:rPr>(.-)</w:rPr>")
      if rPr then
        -- 检查底色/高亮 - 增强匹配模式
        local highlight = nil

        -- 尝试多种可能的highlight标签格式
        highlight = string.match(rPr, "<w:highlight%s+w:val=\"([^\"]+)\"")
        if not highlight then
          highlight = string.match(rPr, "<w:highlight%s+w:val=\"([^\"]+)\"/>")
        end
        if not highlight then
          highlight = string.match(rPr, "<w:highlight[^>]*w:val=\"([^\"]+)\"[^>]*>")
        end
        if not highlight then
          highlight = string.match(rPr, "<w:highlight[^>]*w:val=\"([^\"]+)\"[^>]*/>")
        end

        if highlight then
          run.format.highlight = highlight
          highlight_count = highlight_count + 1
        end

        -- 检查填充颜色（另一种底色表示方法）- 增强匹配模式
        local shd = nil

        -- 尝试多种可能的shd标签格式
        shd = string.match(rPr, "<w:shd%s+w:fill=\"([^\"]+)\"")
        if not shd then
          shd = string.match(rPr, "<w:shd[^>]+w:fill=\"([^\"]+)\"[^>]*/>")
        end
        if not shd then
          shd = string.match(rPr, "<w:shd[^>]*w:fill=\"([^\"]+)\"[^>]*>")
        end
        if not shd then
          -- 尝试匹配w:color属性
          shd = string.match(rPr, "<w:color%s+w:val=\"([^\"]+)\"")
        end
        if not shd then
          shd = string.match(rPr, "<w:color[^>]*w:val=\"([^\"]+)\"[^>]*/>")
        end

        -- 检查底纹颜色
        if not shd then
          shd = string.match(rPr, "<w:background%s+w:color=\"([^\"]+)\"")
        end

        if shd and shd ~= "000000" and shd ~= "auto" and not run.format.highlight then
          run.format.highlight = shd
          highlight_count = highlight_count + 1
        end

        -- 检查删除线 - 更加严格的匹配
        local has_strike = false
        -- 检查所有可能的删除线标签
        if string.find(rPr, "<w:strike[^>]*>") or
           string.find(rPr, "<w:dstrike[^>]*>") or
           string.find(rPr, "<w:strike%s+w:val=\"true\"") or
           string.find(rPr, "<w:dstrike%s+w:val=\"true\"") or
           string.find(rPr, "<w:strike/>") or
           string.find(rPr, "<w:dstrike/>") then
          has_strike = true
        end

        -- 保存删除线原始XML用于调试
        if has_strike then
          run.format.strike = true
          strike_count = strike_count + 1
          table.insert(strike_regions, {xml = r_content, paragraph_id = p_count})
        end
      end

      -- 提取文本
      local text = string.match(r_content, "<w:t[^>]*>(.-)</w:t>")
      if text then
        run.text = decode_html_entities(text)  -- 解码HTML实体
        table.insert(paragraph.runs, run)
      end
    end

    table.insert(paragraphs, paragraph)
    
    ::continue::
  end

  -- 保存表格过滤统计信息到调试文件
  local table_filter_file = io.open(debug_dir .. "\\table_filter_stats.txt", "w")
  if table_filter_file then
    table_filter_file:write("表格过滤统计信息\n")
    table_filter_file:write("===================\n")
    table_filter_file:write("总段落数量: " .. p_count .. "\n")
    table_filter_file:write("表格段落数量: " .. table_paragraph_count .. "\n")
    table_filter_file:write("非表格段落数量: " .. non_table_paragraph_count .. "\n")
    table_filter_file:write("表格段落占比: " .. string.format("%.2f", (table_paragraph_count / p_count) * 100) .. "%\n")
    table_filter_file:close()
  end

  -- 保存删除线区域信息到调试文件
  local strike_debug_file = io.open(debug_dir .. "\\strike_regions.txt", "w")
  if strike_debug_file then
    for i, region in ipairs(strike_regions) do
      strike_debug_file:write("===== 删除线区域 #" .. i .. " (段落ID: " .. region.paragraph_id .. ") =====\n")
      strike_debug_file:write(region.xml .. "\n\n")
    end
    strike_debug_file:close()
  end

  -- 构建纯文本和效果信息
  local plain_text = ""
  local styled_text = ""
  local effects = {
    color_regions = {},
    strike_regions = {}
  }

  -- 文本当前位置计数器
  local current_pos = 1

  for _, paragraph in ipairs(paragraphs) do
    local para_text = ""
    local para_styled_text = ""

    -- 收集段落中的所有文本，用于检查是否包含角色-CV
    local full_paragraph_text = ""
    for _, run in ipairs(paragraph.runs) do
      full_paragraph_text = full_paragraph_text .. run.text
    end

    -- 检查段落是否包含角色-CV格式 - 简化匹配模式
    -- 只要【】里面有破折号就识别
    local has_role_cv_format = false

    -- 先移除所有可能的格式标签，避免干扰角色-CV格式的判断
    local clean_paragraph_text = full_paragraph_text:gsub("%[#.-]", ""):gsub("%[#%]", ""):gsub("%[x%]", ""):gsub("%[/x%]", "")

    has_role_cv_format = clean_paragraph_text:find("【") and clean_paragraph_text:find("】") and
                        (clean_paragraph_text:find("-") or clean_paragraph_text:find("－"))

    -- 检查是否有【角色-CV】格式，并提取内容
    local start_idx = 1
    while true do
      -- 从当前位置查找下一个【
      start_idx = clean_paragraph_text:find("【", start_idx)
      if not start_idx then break end

      -- 找到对应的】
      local end_idx = clean_paragraph_text:find("】", start_idx)
      if not end_idx then break end

      -- 提取【】内的内容
      local content = clean_paragraph_text:sub(start_idx, end_idx)

      -- 检查内容中是否包含-或－
      if content:find("-") or content:find("－") then
        -- 将清理后的文本中的位置映射回原始文本
        -- 找出原文本中相应的【】位置
        local original_start_idx = nil
        local original_end_idx = nil
        local bracket_count = 0

        -- 查找对应的【在原文中的位置
        for i = 1, #full_paragraph_text do
          if full_paragraph_text:sub(i, i) == "【" then
            bracket_count = bracket_count + 1
            -- 修复匹配逻辑：计算清理文本中的【数量，而不是使用正则
            local clean_brackets_before = select(2, clean_paragraph_text:sub(1, start_idx):gsub("【", ""))
            if bracket_count == clean_brackets_before then
              original_start_idx = i
              break
            end
          end
        end

        -- 查找对应的】在原文中的位置
        if original_start_idx then
          for i = original_start_idx, #full_paragraph_text do
            if full_paragraph_text:sub(i, i) == "】" then
              original_end_idx = i
              break
            end
          end
        end

        -- 如果找到了对应的位置，添加到效果列表
        if original_start_idx and original_end_idx then
          -- 计算在plain_text中的位置
          local plain_pos_start = current_pos + original_start_idx - 1
          local plain_pos_end = current_pos + original_end_idx

          -- 添加颜色区域
          table.insert(effects.color_regions, {
            start = plain_pos_start,
            ["end"] = plain_pos_end,
            color = "D9D4E8"  -- 使用浅紫色作为默认颜色
          })
        end
      end

      -- 继续查找下一个
      start_idx = end_idx + 1
    end

    -- 追踪是否当前正在处理的内容有删除线格式
    local current_has_strike = false
    local strike_start_pos = nil

    -- 新的处理方式：按颜色分组整理文本运行段
    local color_groups = {}

    -- 第一步：收集所有运行文本及其属性，以便后续处理
    local all_runs = {}
    for _, run in ipairs(paragraph.runs) do
      if run.text and run.text ~= "" then
        table.insert(all_runs, {
          text = run.text,
          color = run.format.highlight,
          has_strike = run.format.strike
        })
        para_text = para_text .. run.text
      end
    end

    -- 第二步：合并相同颜色的连续文本块（即使被分成多个文本运行）
    local i = 1
    while i <= #all_runs do
      local current_run = all_runs[i]
      local merged_group = {
        color = current_run.color,
        text = current_run.text,
        has_strike = current_run.has_strike
      }

      -- 向前查找具有相同颜色和删除线属性的运行文本
      local j = i + 1
      while j <= #all_runs do
        local next_run = all_runs[j]
        -- 只有当颜色和删除线属性都匹配时才合并
        if next_run.color == merged_group.color and next_run.has_strike == merged_group.has_strike then
          merged_group.text = merged_group.text .. next_run.text
          j = j + 1
        else
          break
        end
      end

      -- 添加合并后的组
      table.insert(color_groups, merged_group)

      -- 跳过已处理的运行
      i = j
    end

    -- 对于具有删除线属性的文本，记录到效果区域
    for _, run in ipairs(paragraph.runs) do
      if run.text and run.text ~= "" and run.format.strike then
        -- 记录纯文本中的位置
        local pos_in_text = para_text:find(run.text, 1, true)
        if pos_in_text then
          local pos_start = current_pos + pos_in_text - 1
          local pos_end = pos_start + #run.text

          -- 添加删除线区域
          table.insert(effects.strike_regions, {
            start = pos_start,
            ["end"] = pos_end,
            text = run.text,
            paragraph_id = paragraph.id
          })
        end
      end
    end

    -- 处理各个分组，添加适当的标签
    local para_styled_text = ""
    for _, group in ipairs(color_groups) do
      local styled_group_text = group.text

      -- 添加标签的顺序很重要：先外部标签（背景色），后内部标签（删除线）
      -- 这样可以确保从外到内解析标签，避免嵌套错误

      -- 先添加背景色标签(如果有颜色且满足条件)
      if group.color and (not WordModule.config.strict_role_cv_matching or has_role_cv_format) then
        styled_group_text = "[bg#" .. group.color .. "]" .. styled_group_text .. "[bg#]"
      end

      -- 再添加删除线标签（如果有）
      if group.has_strike then
        styled_group_text = "[x]" .. styled_group_text .. "[/x]"
      end

      -- 添加到段落样式文本
      para_styled_text = para_styled_text .. styled_group_text
    end

    -- 添加段落到总文本，并添加换行符
    if para_text ~= "" then
      -- 检查是否为章节标题，如果是则添加特殊标记
      local is_chapter_text = false
      local clean_para_text = para_text:match("^%s*(.-)%s*$") or para_text
      
      if clean_para_text:match("^第%s*[%d一二三四五六七八九十百千]+%s*[章节集话回]") or
         clean_para_text:match("^章节%s*[%d一二三四五六七八九十百千]+") then
          is_chapter_text = true
      end
      
      if is_chapter_text then
          -- 将章节段落及其标签作为单独存储
          local chapter_info = {
            plain_text = para_text,
            styled_text = para_styled_text,
            position = current_pos,
            is_chapter = true
          }
          
          -- 存储章节信息到effects中
          if not effects.chapters then
            effects.chapters = {}
          end
          table.insert(effects.chapters, chapter_info)
          
          -- 为章节文本添加特殊标记，但保持原有的渲染方式
          plain_text = plain_text .. "[CHAPTER]" .. para_text .. "[/CHAPTER]" .. "\n"
          styled_text = styled_text .. "[CHAPTER]" .. para_styled_text .. "[/CHAPTER]" .. "\n"
          -- 更新位置计数器（包括标记和换行符）
          current_pos = current_pos + #"[CHAPTER]" + #para_text + #"[/CHAPTER]" + 1
      else
          plain_text = plain_text .. para_text .. "\n"
          styled_text = styled_text .. para_styled_text .. "\n"
          -- 更新位置计数器（包括换行符）
          current_pos = current_pos + #para_text + 1
      end
    end
  end

  -- 保存结果文本到调试文件
  local styled_debug_file = io.open(debug_dir .. "\\styled_text.txt", "w")
  if styled_debug_file then
    styled_debug_file:write(styled_text)
    styled_debug_file:close()
  end

  -- 清理临时目录
  if r.JS_Process_ExecuteCommand then
    -- 使用JS API执行命令，确保完全隐藏窗口
    r.JS_Process_ExecuteCommand('rmdir /S /Q "' .. temp_dir .. '" 2>nul', false)
  else
    os.execute('rmdir /S /Q "' .. temp_dir .. '" 2>nul')
  end

  -- 返回纯文本、带样式的文本和效果信息
  return styled_text, effects
end

-- 去除重复的颜色标签（处理Word分页可能导致的重复标签）
function WordModule.merge_duplicated_tags(text)
  local result = text

  -- 匹配模式：[bg#XXXXXX][bg#]紧接着又有[bg#XXXXXX]
  result = result:gsub("%[bg#([0-9A-Fa-f]+)%]%[bg#%]%[bg#%1%]", "[bg#%1]")

  -- 还可以根据需要添加其他清理规则

  return result
end

-- 优化背景色标签应用，确保相同颜色只应用一次标签
function WordModule.optimize_highlight_tags(text)
  local result = text

  -- 第一步：预处理 - 直接替换重复的相同颜色标签序列
  -- 例如: [bg#A47C46][bg#][bg#A47C46] -> [bg#A47C46]
  result = result:gsub("%[bg#([0-9A-Fa-f]+)%]%[bg#%]%[bg#%1%]", "[bg#%1]")

  -- 处理连续的相同颜色标签 [bg#A47C46]...[bg#][bg#A47C46]...
  -- 使用更宽松的匹配模式，能够匹配任意内容（不仅仅是结尾带括号的）
  local pattern = "%[bg#([0-9A-Fa-f]+)%](.-%)%[bg#%]%s*%[bg#%1%]"
  while result:match(pattern) do
    result = result:gsub(pattern, "[bg#%1]%2")
  end

  -- 更强大的模式，可以处理更一般的情况
  local general_pattern = "%[bg#([0-9A-Fa-f]+)%](.-%)%[bg#%]%[bg#%1%]"
  local iterations = 0
  local changed = true

  -- 多次应用，直到没有变化为止
  while changed and iterations < 10 do
    local new_result = result:gsub(general_pattern, "[bg#%1]%2")
    changed = (new_result ~= result)
    result = new_result
    iterations = iterations + 1
  end

  -- 针对您提供的案例"好好好"的特定模式
  local specific_pattern = "(%[bg#([0-9A-Fa-f]+)%][^%[]+)%[bg#%]%[bg#%2%]([^%[]+)%[bg#%]%[bg#%2%]"
  result = result:gsub(specific_pattern, "%1%3[bg#]")

  -- 第二步：标准处理 - 以段落为单位处理
  local paragraphs = {}
  for paragraph in result:gmatch("([^\n]*)\n?") do
    if paragraph ~= "" then
      table.insert(paragraphs, paragraph)
    end
  end

  for p_idx, paragraph in ipairs(paragraphs) do
    local current_color = nil
    local has_open_tag = false
    local processed = ""
    local i = 1

    while i <= #paragraph do
      -- 检查背景色开始标签
      local bg_start, bg_end, color = paragraph:find("%[bg#([0-9A-Fa-f]+)%]", i)
      if bg_start and bg_start == i then
        -- 如果已经有相同颜色的开放标签，忽略这个
        if has_open_tag and current_color == color then
          i = bg_end + 1
        else
          -- 如果有不同颜色的开放标签，先关闭它
          if has_open_tag then
            processed = processed .. "[bg#]"
          end
          -- 开启新颜色
          current_color = color
          has_open_tag = true
          processed = processed .. "[bg#" .. color .. "]"
          i = bg_end + 1
        end
      -- 检查背景色结束标签
      elseif paragraph:find("%[bg#%]", i) == i then
        -- 只在有开放标签时处理关闭标签
        if has_open_tag then
          -- 检查后面是否紧跟着相同颜色的开放标签
          local next_start, next_end, next_color = paragraph:find("%[bg#([0-9A-Fa-f]+)%]", i + 5)
          if next_start and next_start == i + 5 and next_color == current_color then
            -- 忽略这对关闭-打开标签，保持连续性
            i = next_end + 1
          elseif next_start and next_start <= i + 15 and next_color == current_color then
            -- 检查是否在较短距离内有相同颜色的标签（最多10个字符），如果有，也保持连续性
            -- 提取中间的内容，确保不包含其他标签
            local middle_content = paragraph:sub(i + 5, next_start - 1)
            if not middle_content:find("%[") and not middle_content:find("%]") then
              processed = processed .. middle_content
              i = next_end + 1
              -- 跳过此次关闭标签，保持当前颜色标签的开放状态
              goto continue
            end
          end

          -- 正常关闭标签
          has_open_tag = false
          current_color = nil
          processed = processed .. "[bg#]"
          i = i + 5
        else
          -- 没有开放标签，忽略这个关闭标签
          i = i + 5
        end
      else
        -- 复制普通字符
        processed = processed .. paragraph:sub(i, i)
        i = i + 1
      end

      ::continue::
    end

    -- 确保段落结束时关闭开放的标签
    if has_open_tag then
      processed = processed .. "[bg#]"
    end

    paragraphs[p_idx] = processed
  end

  -- 第三步：额外优化 - 合并跨段落的相同颜色区域
  -- 检查每个段落结束和下一个段落开始的标签
  for i = 1, #paragraphs - 1 do
    -- 如果段落尾有关闭标签，下一段首有相同颜色的开放标签，去掉关闭+开放
    local color1, color2
    local has_closing = paragraphs[i]:match("%[bg#%]$")
    local has_opening = paragraphs[i+1]:match("^%[bg#([0-9A-Fa-f]+)%]")

    if has_closing and has_opening then
      -- 查找最后一个颜色标签
      local last_color = nil
      for color in paragraphs[i]:gmatch("%[bg#([0-9A-Fa-f]+)%]") do
        last_color = color
      end

      if last_color == has_opening then
        -- 移除段落尾的关闭标签
        paragraphs[i] = paragraphs[i]:gsub("%[bg#%]$", "")
        -- 移除下一段首的开放标签
        paragraphs[i+1] = paragraphs[i+1]:gsub("^%[bg#" .. has_opening .. "%]", "")
      end
    end
  end

  -- 拼接处理后的段落
  result = table.concat(paragraphs, "\n")

  -- 最后一步：确保所有标签都正确闭合
  local tag_stack = {}
  local final_result = ""

  for i = 1, #result do
    local char = result:sub(i, i)
    final_result = final_result .. char

    -- 检查标签开始
    if char == "[" and result:sub(i, i+3) == "[bg#" and result:sub(i+4, i+4):match("[0-9A-Fa-f]") then
      -- 提取颜色
      local color_end = result:find("%]", i)
      if color_end then
        local color = result:sub(i+4, color_end-1)
        table.insert(tag_stack, color)
      end
    -- 检查标签结束
    elseif char == "]" and result:sub(i-4, i) == "[bg#]" then
      if #tag_stack > 0 then
        table.remove(tag_stack)
      end
    end
  end

  -- 确保所有打开的标签都关闭了
  while #tag_stack > 0 do
    table.remove(tag_stack)
    final_result = final_result .. "[bg#]"
  end

  return final_result
end

-- 清理角色CV列表中的颜色标签
function WordModule.clean_cv_tags(text)
  -- 查找所有的角色-CV标记
  local cleaned_text = text

  -- 查找所有【】中的内容
  local start_pos = 1
  while true do
    local bracket_start = cleaned_text:find("【", start_pos)
    if not bracket_start then break end

    local bracket_end = cleaned_text:find("】", bracket_start)
    if not bracket_end then break end

    -- 提取【】区域的文本
    local bracket_content = cleaned_text:sub(bracket_start, bracket_end)

    -- 只有包含-或－的才视为角色-CV格式
    if bracket_content:find("-") or bracket_content:find("－") then
      -- 清理这个区域内的颜色标签
      local clean_content = bracket_content:gsub("%[bg#[0-9A-Fa-f]+%]", ""):gsub("%[bg#%]", "")

      -- 替换原始文本
      cleaned_text = cleaned_text:sub(1, bracket_start - 1) .. clean_content .. cleaned_text:sub(bracket_end + 1)

      -- 调整下一次搜索的起始位置
      start_pos = bracket_start + #clean_content
    else
      -- 不是角色-CV格式，继续搜索
      start_pos = bracket_end + 1
    end
  end

  return cleaned_text
end

-- 保存到文件
function WordModule.save_to_file(text, save_dir, file_name)
  -- 确保目录存在
  if save_dir and save_dir ~= "" then
    if r.JS_Process_ExecuteCommand then
      r.JS_Process_ExecuteCommand('mkdir "' .. save_dir .. '" 2>nul', false)
    else
      os.execute('mkdir "' .. save_dir .. '" 2>nul')
    end
  end

  -- 构建完整的文件路径
  local file_path
  if save_dir and save_dir ~= "" then
    file_path = save_dir .. "\\" .. file_name .. ".txt"
  else
    file_path = file_name .. ".txt"
  end

  -- 尝试打开文件进行写入
  local file = io.open(file_path, "w")
  if not file then
    return false, "无法创建文件: " .. file_path
  end

  -- 写入文本内容
  file:write(text)
  file:close()

  return true, file_path
end

-- 处理标点符号导致的标签问题
function WordModule.fix_punctuation_tags(text)
  if not text then return text end

  local result = text

  -- 中文标点符号集合
  local punctuation = "，。！？；：\"\"''「」『』（）【】《》〈〉"

  -- 先处理常见的问题模式

  -- 1. 修复[/x]后面紧跟着标点符号的情况
  for punct in punctuation:gmatch(".") do
    -- 检测模式：[/x]后面紧跟着标点符号
    result = result:gsub("%[/x%](" .. punct .. ")", punct .. "[/x]")
  end

  -- 2. 修复[bg#]之前有标点但之后也有[/x]的情况
  for punct in punctuation:gmatch(".") do
    -- 检测模式：标点[bg#][/x]
    result = result:gsub("(" .. punct .. ")%[bg#%]%[/x%]", "[/x]" .. punct .. "[bg#]")
  end

  -- 3. 处理特殊的组合模式
  -- [bg#XXXX][x] -> [x][bg#XXXX]
  result = result:gsub("(%[bg#[0-9A-Fa-f]+%])(%[x%])", "%2%1")

  -- 4. 处理[/x][bg#]模式
  result = result:gsub("%[/x%](%[bg#%])", "%1[/x]")

  -- 5. 对于特殊的中文标点，确保它们被包含在删除线内
  for i = 1, #result do
    local char = result:sub(i, i)
    if punctuation:find(char, 1, true) and i > 5 then
      -- 检查前面是否有[/x]标签
      local prev = result:sub(i-5, i-1)
      if prev == "[/x]" then
        -- 将标点移到[/x]之前
        result = result:sub(1, i-6) .. char .. "[/x]" .. result:sub(i+1)
      end
    end
  end

  -- 调试输出
  print("修复了标点符号标签问题")

  return result
end

-- 修复嵌套标签问题
function WordModule.fix_nested_tags(text)
  local result = text

  -- 1. 修复背景色内部的删除线标签嵌套问题（从外到内处理）
  local function fix_bg_with_strike(text)
    -- 这个函数处理[bg#COLOR][x]文本[/x][bg#]模式
    -- 确保背景色标签在外，删除线标签在内
    local pattern = "%[bg#([0-9A-Fa-f]+)%]%[x%](.-?)%[/x%]%[bg#%]"
    local replacement = "[bg#%1][x]%2[/x][bg#]"

    local changed = true
    local result = text
    local iterations = 0

    -- 多次应用模式，直到不再有变化
    while changed and iterations < 10 do
      local new_result = result:gsub(pattern, replacement)
      changed = (new_result ~= result)
      result = new_result
      iterations = iterations + 1
    end

    return result
  end

  -- 2. 修复错误嵌套的删除线和背景色标签
  -- 例如: [x][bg#COLOR]文本[bg#][/x] 修正为 [bg#COLOR][x]文本[/x][bg#]
  local function fix_strike_with_bg(text)
    local pattern = "%[x%]%[bg#([0-9A-Fa-f]+)%](.-?)%[bg#%]%[/x%]"
    local replacement = "[bg#%1][x]%2[/x][bg#]"

    local changed = true
    local result = text
    local iterations = 0

    -- 多次应用模式，直到不再有变化
    while changed and iterations < 10 do
      local new_result = result:gsub(pattern, replacement)
      changed = (new_result ~= result)
      result = new_result
      iterations = iterations + 1
    end

    return result
  end

  -- 3. 修复多余的背景色标签
  -- [bg#COLOR]...[bg#][bg#COLOR]...[bg#] 修改为 [bg#COLOR]......[bg#]
  local function fix_duplicate_bg(text)
    local pattern = "%[bg#([0-9A-Fa-f]+)%](.-?)%[bg#%]%[bg#%1%](.-?)%[bg#%]"
    local replacement = "[bg#%1]%2%3[bg#]"

    local changed = true
    local result = text
    local iterations = 0

    -- 多次应用模式，直到不再有变化
    while changed and iterations < 10 do
      local new_result = result:gsub(pattern, replacement)
      changed = (new_result ~= result)
      result = new_result
      iterations = iterations + 1
    end

    return result
  end

  -- 4. 专门处理镶套问题：背景色中的删除线被错误分割
  -- 例如: [bg#COLOR]文本[x]部分1[bg#][/x][bg#COLOR]部分2[bg#]
  -- 修正为: [bg#COLOR][x]部分1[/x]部分2[bg#]
  local function fix_nested_bg_strike(text)
    local pattern = "%[bg#([0-9A-Fa-f]+)%](.-?)%[x%](.-?)%[bg#%]%[/x%]%[bg#%1%](.-?)%[bg#%]"
    local replacement = "[bg#%1]%2[x]%3[/x]%4[bg#]"

    local changed = true
    local result = text
    local iterations = 0

    -- 多次应用模式，直到不再有变化
    while changed and iterations < 10 do
      local new_result = result:gsub(pattern, replacement)
      changed = (new_result ~= result)
      result = new_result
      iterations = iterations + 1
    end

    return result
  end

  -- 5. 处理背景色标签内部的嵌套删除线标签
  -- 例如: [bg#COLOR]文本1[x]文本2[bg#][/x][bg#COLOR]文本3[bg#]
  -- 修正为: [bg#COLOR]文本1[x]文本2[/x]文本3[bg#]
  local function fix_bg_nested_strike(text)
    local pattern = "%[bg#([0-9A-Fa-f]+)%](.-?)%[x%](.-?)%[bg#%]%[/x%]%[bg#%1%](.-?)%[bg#%]"
    local replacement = "[bg#%1]%2[x]%3[/x]%4[bg#]"

    local changed = true
    local result = text
    local iterations = 0

    -- 多次应用模式，直到不再有变化
    while changed and iterations < 10 do
      local new_result = result:gsub(pattern, replacement)
      changed = (new_result ~= result)
      result = new_result
      iterations = iterations + 1
    end

    return result
  end

  -- 6. 全面修复特殊案例中的标签嵌套问题
  local function fix_complex_cases(text)
    -- 先使用两轮处理，确保文本被正确解析
    local patterns = {
      -- 修复背景色中错误的删除线标签关闭
      {
        pattern = "(%[bg#[0-9A-Fa-f]+%].-%)%[x%].-%)%[bg#%])%[/x%]",
        replacement = "%1"
      },
      -- 将背景色内的删除线正确包围
      {
        pattern = "%[bg#([0-9A-Fa-f]+)%](.-?)%[x%](.-?)%[bg#%]",
        replacement = "[bg#%1]%2[x]%3[/x][bg#]"
      },
      -- 修复连续背景色标签中的删除线
      {
        pattern = "%[bg#([0-9A-Fa-f]+)%](.-?)%[x%](.-?)%[bg#%]%[/x%]%[bg#%1%]",
        replacement = "[bg#%1]%2[x]%3[/x]"
      },
      -- 删除多余的删除线结束标签
      {
        pattern = "%[bg#([0-9A-Fa-f]+)%]%[x%](.-?)%[/x%](.-?)%[/x%]%[bg#%]",
        replacement = "[bg#%1][x]%2%3[/x][bg#]"
      }
    }

    local result = text
    for _, pattern_item in ipairs(patterns) do
      local changed = true
      local iterations = 0

      while changed and iterations < 10 do
        local new_result = result:gsub(pattern_item.pattern, pattern_item.replacement)
        changed = (new_result ~= result)
        result = new_result
        iterations = iterations + 1
      end
    end

    return result
  end

  -- 应用所有修复函数 - 从外到内的顺序处理标签
  -- 1. 先处理背景色相关的问题
  result = fix_duplicate_bg(result)
  result = fix_nested_bg_strike(result)
  result = fix_bg_nested_strike(result)

  -- 2. 然后处理标签嵌套问题
  result = fix_strike_with_bg(result)
  result = fix_bg_with_strike(result)

  -- 3. 最后处理复杂情况
  result = fix_complex_cases(result)

  -- 清理可能的遗漏问题
  -- 确保标签正确关闭
  result = result:gsub("%[bg#([0-9A-Fa-f]+)%](.-[^%]])$", "[bg#%1]%2[bg#]") -- 确保背景色标签关闭
  result = result:gsub("%[x%](.-[^%]])$", "[x]%2[/x]") -- 确保删除线标签关闭

  -- 处理背景色标签中间断开的情况
  result = result:gsub("%[bg#([0-9A-Fa-f]+)%](.-?)%[bg#%]([^%[].-?)%[bg#%1%]", "[bg#%1]%2%3")

  return result
end

-- 快速处理功能 - 提取并保存docx内容到文件
function WordModule.process_docx(file_path, save_path)
  -- 解析文档
  local styled_text, effects = WordModule.parse_docx(file_path)

  -- 如果解析成功（没有返回错误消息）
  if styled_text and type(styled_text) == "string" and not styled_text:match("^无法") then
    -- 按照从外到内的顺序处理标签

    -- 1. 先优化背景色标签，确保相同颜色区域只应用一次标签
    styled_text = WordModule.optimize_highlight_tags(styled_text)

    -- 2. 处理分页导致的重复标签问题
    styled_text = WordModule.merge_duplicated_tags(styled_text)

    -- 3. 修复嵌套标签问题 - 先处理背景色与删除线的嵌套关系
    styled_text = WordModule.fix_nested_tags(styled_text)

    -- 4. 处理角色CV列表中的标签问题
    styled_text = WordModule.clean_cv_tags(styled_text)

    -- 5. 最后处理可能的标点符号问题
    styled_text = WordModule.fix_punctuation_tags(styled_text)

    -- 获取基本文件名用于保存
    local file_name = string.match(file_path, "[^\\]+$") or "document"
    local base_name = string.gsub(file_name, "%.docx$", "")

    -- 保存文件 - 保存带样式的文本
    local success, result = WordModule.save_to_file(styled_text, save_path, base_name)

    -- 返回带样式的文本和效果元数据信息
    if success then
      return styled_text, effects, result
    else
      return styled_text, effects, nil
    end
  else
    -- 解析失败，返回错误消息
    return nil, nil, styled_text
  end
end

-- 主要函数，同时检查元数据功能是否可用
function WordModule.run(file_path, save_path)
  -- 尝试加载text_utils模块，用于元数据支持
  local text_utils = nil
  local text_utils_loaded = false

  -- 如果启用了元数据功能，尝试加载text_utils模块
  if WordModule.use_metadata and WordModule.use_metadata() then
    text_utils = package.loaded.text_utils
    text_utils_loaded = (text_utils ~= nil)
  end

  -- 处理文档
  local text, effects, save_path = WordModule.process_docx(file_path, save_path)

  -- 如果成功解析，并且已加载text_utils模块，使用元数据功能
  if text and effects and text_utils_loaded and text_utils.apply_text_effects then
    local text_id = text_utils.apply_text_effects(text, effects)
    return text_id, text, save_path
  else
    -- 如果不支持元数据或解析失败，只返回文本和保存路径
    return nil, text, save_path
  end
end

-- 元数据功能控制
function WordModule.use_metadata()
  -- 默认启用元数据功能
  return true  -- 可以通过修改这里来全局禁用元数据功能
end

-- 应用文本效果并提取章节信息
function WordModule.apply_text_effects(text)
  if not text or text == "" then
    return {chapters = {}}
  end
  
  local chapters = {}
  
  -- 提取章节信息
  -- 1. 查找[CHAPTER]...[/CHAPTER]标记的章节
  for chapter_content in text:gmatch("%[CHAPTER%](.-)%[/CHAPTER%]") do
    table.insert(chapters, {
      plain_text = chapter_content,
      styled_text = "[CHAPTER]" .. chapter_content .. "[/CHAPTER]",
      type = "marked_chapter"
    })
  end
  
  -- 2. 查找传统格式的章节（第X章、章节X等）
  for line in text:gmatch("[^\n]+") do
    local trimmed_line = line:match("^%s*(.-)%s*$") or line
    if trimmed_line:match("^第%s*[%d一二三四五六七八九十百千]+%s*[章节集话回]") or
       trimmed_line:match("^章节%s*[%d一二三四五六七八九十百千]+") then
      table.insert(chapters, {
        plain_text = trimmed_line,
        styled_text = line,
        type = "traditional_chapter"
      })
    end
  end
  
  return {
    chapters = chapters
  }
end

-- 导出模块
return WordModule