-- REAPER Script: Move Items to Edit Cursor
-- Description: Select all audio items to the right of edit cursor and move them to cursor position
-- Author: Assistant
-- Version: 1.0

function main()
    -- Get the current edit cursor position
    local cursor_pos = reaper.GetCursorPosition()
    
    -- Find the region that contains the edit cursor
    local region_start, region_end = nil, nil
    local region_count = reaper.CountProjectMarkers(0)
    
    for i = 0, region_count - 1 do
        local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(i)
        if isrgn and pos <= cursor_pos and cursor_pos <= rgnend then
            region_start = pos
            region_end = rgnend
            break
        end
    end
    
    -- If cursor is not in any region, exit
    if not region_start then
        return
    end
    
    -- Clear current selection
    reaper.SelectAllMediaItems(0, false)
    
    -- Get the number of tracks in the project
    local track_count = reaper.CountTracks(0)
    
    local items_to_move = {}
    local leftmost_pos = math.huge
    
    -- Loop through all tracks
    for i = 0, track_count - 1 do
        local track = reaper.GetTrack(0, i)
        local item_count = reaper.CountTrackMediaItems(track)
        
        -- Loop through all items on this track
        for j = 0, item_count - 1 do
            local item = reaper.GetTrackMediaItem(track, j)
            local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
            local item_end = item_pos + reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
            
            -- Check if item is to the right of cursor AND within the same region
            if item_pos > cursor_pos and item_pos < region_end and item_end <= region_end then
                -- Select the item
                reaper.SetMediaItemSelected(item, true)
                table.insert(items_to_move, item)
                
                -- Track the leftmost position of selected items
                if item_pos < leftmost_pos then
                    leftmost_pos = item_pos
                end
            end
        end
    end
    
    -- If we found items to move
    if #items_to_move > 0 then
        -- Calculate the offset needed to move leftmost item to cursor
        local offset = cursor_pos - leftmost_pos
        
        -- Begin undo block
        reaper.Undo_BeginBlock()
        
        -- Move all selected items
        for _, item in ipairs(items_to_move) do
            local current_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
            local new_pos = current_pos + offset
            reaper.SetMediaItemInfo_Value(item, "D_POSITION", new_pos)
        end
        
        -- Update the arrangement
        reaper.UpdateArrange()
        
        -- Adjust region end time based on movement
        local move_distance = leftmost_pos - cursor_pos
        if move_distance > 0 then
            -- Find the region index that contains the cursor
            local region_index = -1
            for i = 0, region_count - 1 do
                local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(i)
                if isrgn and pos <= cursor_pos and cursor_pos <= rgnend then
                    region_index = markrgnindexnumber
                    break
                end
            end
            
            -- Shorten the region end by the movement distance
            if region_index >= 0 then
                local new_region_end = region_end - move_distance
                reaper.SetProjectMarker(region_index, true, region_start, new_region_end, "")
            end
        end
        
        -- End undo block
        reaper.Undo_EndBlock("Move items to edit cursor", -1)
        
    end
    
    -- Clear selection after operation
    reaper.SelectAllMediaItems(0, false)
end

-- Run the main function
main()