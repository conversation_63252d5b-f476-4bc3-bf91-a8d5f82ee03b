-- Toggle Region Color Script
-- 功能：选中的区间标记改成黄色，再次点击改成默认颜色
-- Author: Generated Script
-- Version: 1.0

-- 定义颜色常量
local YELLOW_COLOR = reaper.ColorToNative(255, 255, 0)|0x1000000  -- 黄色 (RGB格式)
local DEFAULT_COLOR = 0  -- 默认颜色 (无颜色)

-- 调试：显示颜色值
reaper.ShowConsoleMsg(string.format("脚本启动 - 黄色值: %d, 默认色值: %d\n", YELLOW_COLOR, DEFAULT_COLOR))

-- 主函数
function main()
    -- 获取当前选中的区间数量
    local num_regions, num_markers = reaper.CountProjectMarkers(0)
    
    if num_regions == 0 then
        reaper.ShowMessageBox("没有找到任何区间标记", "提示", 0)
        return
    end
    
    -- 获取鼠标位置和时间位置
    local mouse_x, mouse_y = reaper.GetMousePosition()
    local mouse_time = reaper.BR_GetMouseCursorContext_Position()
    
    -- 如果无法获取鼠标时间位置，使用光标位置作为备选
    local target_time
    if mouse_time and mouse_time >= 0 then
        target_time = mouse_time
        reaper.ShowConsoleMsg(string.format("使用鼠标位置时间: %.3f\n", target_time))
    else
        target_time = reaper.GetCursorPosition()
        reaper.ShowConsoleMsg(string.format("使用光标位置时间: %.3f\n", target_time))
    end
    
    local regions_modified = 0
    local found_region = false
    
    -- 遍历所有标记和区间
    for i = 0, num_regions + num_markers - 1 do
        local retval, isrgn, pos, rgnend, name, markrgnindexnumber, color = reaper.EnumProjectMarkers3(0, i)
        
        if retval and isrgn then  -- 如果是区间
            -- 检查目标时间是否在区间范围内
            local is_target_region = (target_time >= pos and target_time <= rgnend)
            
            if is_target_region then
                found_region = true
                
                -- 调试信息：显示当前颜色值
                reaper.ShowConsoleMsg(string.format("当前区间颜色值: %d, 黄色值: %d, 默认色值: %d\n", color, YELLOW_COLOR, DEFAULT_COLOR))
                
                -- 切换颜色逻辑修正
                local new_color
                -- 直接比较颜色值
                if color == YELLOW_COLOR then
                    -- 如果当前是黄色，改为默认颜色
                    new_color = DEFAULT_COLOR
                    reaper.ShowConsoleMsg("切换到默认颜色\n")
                else
                    -- 如果当前是默认颜色或其他颜色，改为黄色
                    new_color = YELLOW_COLOR
                    reaper.ShowConsoleMsg("切换到黄色\n")
                end
                
                -- 设置新颜色 - 使用SetProjectMarkerByIndex函数
                local success = reaper.SetProjectMarkerByIndex(0, i, isrgn, pos, rgnend, markrgnindexnumber, name, new_color)
                reaper.ShowConsoleMsg(string.format("设置颜色结果: %s, 新颜色值: %d\n", success and "成功" or "失败", new_color))
                
                -- 验证颜色是否设置成功
                local retval2, isrgn2, pos2, rgnend2, name2, markrgnindexnumber2, color2 = reaper.EnumProjectMarkers3(0, i)
                reaper.ShowConsoleMsg(string.format("设置后实际颜色值: %d\n", color2))
                
                regions_modified = regions_modified + 1
                
                -- 找到第一个匹配的区间后退出
                break
            end
        end
    end
    
    if regions_modified > 0 then
        -- 更新项目显示
        reaper.UpdateArrange()
        reaper.UpdateTimeline()
        
        reaper.ShowConsoleMsg(string.format("成功修改了 %d 个区间的颜色\n", regions_modified))
    else
        if not found_region then
            reaper.ShowMessageBox("目标位置没有找到区间标记\n请将鼠标放在区间标记内或将光标放在区间内", "提示", 0)
        end
    end
end

-- 开始撤销块
reaper.Undo_BeginBlock()

-- 执行主函数
main()

-- 结束撤销块
reaper.Undo_EndBlock("切换区间颜色", -1)