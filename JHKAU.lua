--[[
功能：在主外部编辑器中打开对象，并自动停止工程，离线音频，将音频时间传入至剪贴板，以便快速找到对应位置
作者：玩符的僵尸
扣扣：2543288153
]]

local v0 = tonumber; local v1 = string.byte; local v2 = string.char; local v3 = string.sub; local v4 = string.gsub; local v5 =
string.rep; local v6 = table.concat; local v7 = table.insert; local v8 = math.ldexp; local v9 = getfenv or
function() return _ENV; end; local v10 = setmetatable; local v11 = pcall; local v12 = select; local v13 = unpack or
table.unpack; local v14 = tonumber; local function v15(v16, v17, ...)
    local v18 = 1; local v19; v16 = v4(v3(v16, 5), "..",
        function(v30) if (v1(v30, 2) == 81) then
                local v80 = 0; while true do if (v80 == 0) then
                        v19 = v0(v3(v30, 1, 1)); return "";
                    end end
            else
                local v81 = v2(v0(v30, 16)); if v19 then
                    local v90 = v5(v81, v19); v19 = nil; return v90;
                else return v81; end
            end end); local function v20(v31, v32, v33) if v33 then
            local v82 = (v31 / (2 ^ (v32 - (2 - 1)))) % ((5 - 3) ^ (((v33 - (1638 - (1523 + 114))) - (v32 - (1 - 0))) + (2 - 1))); return
            v82 - (v82 % (620 - (555 + 58 + 6)));
        else
            local v83 = 931 - (857 + 74); local v84; while true do if (v83 == (568 - (367 + (286 - 85)))) then
                    v84 = (929 - (214 + 713)) ^ (v32 - (1 + 0)); return (((v31 % (v84 + v84)) >= v84) and (1 + 0)) or
                    (877 - (282 + (1660 - (68 + 997))));
                end end
        end end
    local function v21()
        local v34 = v1(v16, v18, v18); v18 = v18 + 1; return v34;
    end
    local function v22()
        local v35, v36 = v1(v16, v18, v18 + 2); v18 = v18 + (1272 - (226 + 1044)); return (v36 * (1114 - 858)) + v35;
    end
    local function v23()
        local v37 = 0; local v38; local v39; local v40; local v41; while true do
            if (v37 == (117 - (32 + 85))) then
                v38, v39, v40, v41 = v1(v16, v18, v18 + 3); v18 = v18 + 4 + 0; v37 = 1 + 0;
            end
            if (v37 == ((1758 - 800) - (892 + 65))) then return (v41 * (40022563 - 23245347)) + (v40 * (121138 - 55602)) +
                (v39 * 256) + v38; end
        end
    end
    local function v24()
        local v42 = 350 - (87 + 263); local v43; local v44; local v45; local v46; local v47; local v48; while true do
            if (v42 == (183 - (24 + 43 + 113))) then
                if (v47 == 0) then if (v46 == (0 - 0)) then return v48 * ((0 - 0) + 0); else
                        local v106 = 0 + (0 - 0); while true do if (v106 == 0) then
                                v47 = 2 - 1; v45 = 0 + 0; break;
                            end end
                    end elseif (v47 == (8135 - 6088)) then return ((v46 == ((3734 - 2782) - (802 + 150))) and (v48 * ((1 + 0 + 0) / (0 - 0)))) or
                    (v48 * NaN); end
                return v8(v48, v47 - (1855 - 832)) * (v45 + (v46 / ((2 + 0 + 0) ^ (1049 - (915 + 82)))));
            end
            if (v42 == ((0 + 0) - (0 - 0))) then
                v43 = v23(); v44 = v23(); v42 = (1748 - (760 + 987)) + 0;
            end
            if (v42 == (2 - 0)) then
                v47 = v20(v44, 1208 - (1069 + 118), 70 - 39); v48 = ((v20(v44, 891 - (814 + 45)) == ((1915 - (1789 + 124)) - 1)) and -(1 - 0)) or
                (1 + 0); v42 = 888 - (261 + 624);
            end
            if (v42 == (1 + 0)) then
                v45 = (2136 - (87 + 968)) - (1020 + 60); v46 = (v20(v44, 1 - (0 - 0), 20 + 0) * (((720 + 73) - (368 + (1189 - (745 + 21)))) ^ (100 - 68))) +
                v43; v42 = (45 - 25) - (10 + 8);
            end
        end
    end
    local function v25(v49)
        local v50 = 1413 - (447 + 966); local v51; local v52; while true do
            if ((5 - 3) == v50) then
                v52 = {}; for v91 = 1818 - ((1717 - (9 + 5)) + 114), #v51 do v52[v91] = v2(v1(v3(v51, v91, v91))); end
                v50 = 704 - (376 + 325);
            end
            if (v50 == (4 - 1)) then return v6(v52); end
            if (v50 == (0 - (376 - (85 + 291)))) then
                v51 = nil; if not v49 then
                    v49 = v23(); if (v49 == ((1265 - (243 + 1022)) + (0 - 0))) then return ""; end
                end
                v50 = 1;
            end
            if (v50 == 1) then
                v51 = v3(v16, v18, (v18 + v49) - (2 - 1)); v18 = v18 + v49; v50 = 2 + 0;
            end
        end
    end
    local v26 = v23; local function v27(...) return { ... }, v12("#", ...); end
    local function v28()
        local v53 = (function() return 0 - 0; end)(); local v54 = (function() return; end)(); local v55 = (function() return; end)(); local v56 = (function() return; end)(); local v57 = (function() return; end)(); local v58 = (function() return; end)(); local v59 = (function() return; end)(); while true do
            if (v53 ~= #".") then else
                local v87 = (function() return 1274 - (388 + 886); end)(); while true do
                    if (v87 == (0 - 0)) then
                        v58 = (function() return v23(); end)(); v59 = (function() return {}; end)(); v87 = (function() return 1; end)();
                    end
                    if ((1 + 0) ~= v87) then else
                        for v107 = #">", v58 do
                            local v108 = (function() return 0; end)(); local v109 = (function() return; end)(); local v110 = (function() return; end)(); local v111 = (function() return; end)(); while true do
                                if (v108 ~= 1) then else
                                    v111 = (function() return nil; end)(); while true do
                                        if (v109 ~= 0) then else
                                            v110 = (function() return v21(); end)(); v111 = (function() return nil; end)(); v109 = (function() return 561 -
                                                (306 + 254); end)();
                                        end
                                        if (v109 == (1 + 0)) then
                                            if (v110 == #"|") then v111 = (function() return v21() ~= (0 - 0); end)(); elseif (v110 == (1469 - (899 + 568))) then v111 = (function() return
                                                    v24(); end)(); elseif (v110 == #"xxx") then v111 = (function() return
                                                    v25(); end)(); end
                                            v59[v107] = (function() return v111; end)(); break;
                                        end
                                    end
                                    break;
                                end
                                if (v108 == 0) then
                                    local v113 = (function() return 0; end)(); while true do
                                        if (v113 == (0 + 0)) then
                                            v109 = (function() return 0; end)(); v110 = (function() return nil; end)(); v113 = (function() return 1; end)();
                                        end
                                        if (v113 ~= 1) then else
                                            v108 = (function() return 2 - 1; end)(); break;
                                        end
                                    end
                                end
                            end
                        end
                        v57[#"gha"] = (function() return v21(); end)(); v87 = (function() return 605 - (268 + 335); end)();
                    end
                    if (v87 ~= (292 - (60 + 230))) then else
                        v53 = (function() return 574 - (426 + 146); end)(); break;
                    end
                end
            end
            if (v53 ~= (1 + 1)) then else
                for v93 = #".", v23() do
                    local v94 = (function() return 1456 - (282 + 1174); end)(); local v95 = (function() return; end)(); local v96 = (function() return; end)(); while true do
                        if ((811 - (569 + 242)) == v94) then
                            local v112 = (function() return 0 - 0; end)(); while true do
                                if (v112 ~= 1) then else
                                    v94 = (function() return 1; end)(); break;
                                end
                                if (v112 ~= 0) then else
                                    v95 = (function() return 0 + 0; end)(); v96 = (function() return nil; end)(); v112 = (function() return 1025 -
                                        (706 + 318); end)();
                                end
                            end
                        end
                        if (1 ~= v94) then else
                            while true do if (v95 == (1251 - (721 + 530))) then
                                    v96 = (function() return v21(); end)(); if (v20(v96, #":", #"\\") ~= (1271 - (945 + 326))) then else
                                        local v130 = (function() return 0 - 0; end)(); local v131 = (function() return; end)(); local v132 = (function() return; end)(); local v133 = (function() return; end)(); while true do
                                            if (v130 == 0) then
                                                local v135 = (function() return 0 + 0; end)(); local v136 = (function() return; end)(); while true do if (v135 ~= (700 - (271 + 429))) then else
                                                        v136 = (function() return 0 + 0; end)(); while true do
                                                            if (v136 ~= 1) then else
                                                                v130 = (function() return 1; end)(); break;
                                                            end
                                                            if (v136 ~= 0) then else
                                                                v131 = (function() return v20(v96, 1502 - (1408 + 92),
                                                                        #"nil"); end)(); v132 = (function() return v20(
                                                                    v96, #"0313", 1092 - (461 + 625)); end)(); v136 = (function() return 1289 -
                                                                    (993 + 295); end)();
                                                            end
                                                        end
                                                        break;
                                                    end end
                                            end
                                            if (v130 == 2) then
                                                if (v20(v132, #" ", #"{") ~= #"|") then else v133[1 + 1] = (function() return
                                                        v59[v133[2]]; end)(); end
                                                if (v20(v132, 1173 - (418 + 753), 1 + 1) ~= #":") then else v133[#"xnx"] = (function() return
                                                        v59[v133[#"nil"]]; end)(); end
                                                v130 = (function() return 3; end)();
                                            end
                                            if (v130 == 1) then
                                                local v137 = (function() return 0; end)(); while true do
                                                    if (v137 ~= (0 + 0)) then else
                                                        v133 = (function() return { v22(), v22(), nil, nil }; end)(); if (v131 == 0) then
                                                            local v142 = (function() return 0 + 0; end)(); local v143 = (function() return; end)(); while true do if (v142 == (529 - (406 + 123))) then
                                                                    v143 = (function() return 0; end)(); while true do if ((1769 - (1749 + 20)) ~= v143) then else
                                                                            v133[#"19("] = (function() return v22(); end)(); v133[#"0313"] = (function() return
                                                                                v22(); end)(); break;
                                                                        end end
                                                                    break;
                                                                end end
                                                        elseif (v131 == #",") then v133[#"xnx"] = (function() return v23(); end)(); elseif (v131 == 2) then v133[#"-19"] = (function() return
                                                                v23() - (2 ^ (4 + 12)); end)(); elseif (v131 == #"19(") then
                                                            local v148 = (function() return 0; end)(); local v149 = (function() return; end)(); while true do if (v148 ~= (1322 - (1249 + 73))) then else
                                                                    v149 = (function() return 0 + 0; end)(); while true do if (v149 ~= 0) then else
                                                                            v133[#"91("] = (function() return v23() -
                                                                                ((1147 - (466 + 679)) ^ (38 - 22)); end)(); v133[#"http"] = (function() return
                                                                                v22(); end)(); break;
                                                                        end end
                                                                    break;
                                                                end end
                                                        end
                                                        v137 = (function() return 2 - 1; end)();
                                                    end
                                                    if (v137 == 1) then
                                                        v130 = (function() return 1902 - (106 + 1794); end)(); break;
                                                    end
                                                end
                                            end
                                            if (v130 == 3) then
                                                if (v20(v132, #"xnx", #"nil") == #":") then v133[#"asd1"] = (function() return
                                                        v59[v133[#"0313"]]; end)(); end
                                                v54[v93] = (function() return v133; end)(); break;
                                            end
                                        end
                                    end
                                    break;
                                end end
                            break;
                        end
                    end
                end
                for v97 = #":", v23() do v55[v97 - #"<"] = (function() return v28(); end)(); end
                return v57;
            end
            if (v53 ~= 0) then else
                local v88 = (function() return 0; end)(); local v89 = (function() return; end)(); while true do if (v88 == (0 + 0)) then
                        v89 = (function() return 0 + 0; end)(); while true do
                            if (v89 == (0 - 0)) then
                                v54 = (function() return {}; end)(); v55 = (function() return {}; end)(); v89 = (function() return 1; end)();
                            end
                            if (v89 ~= (5 - 3)) then else
                                v53 = (function() return #"]"; end)(); break;
                            end
                            if (v89 == (115 - (4 + 110))) then
                                v56 = (function() return {}; end)(); v57 = (function() return { v54, v55, nil, v56 }; end)(); v89 = (function() return 2; end)();
                            end
                        end
                        break;
                    end end
            end
        end
    end
    local function v29(v60, v61, v62)
        local v63 = v60[585 - (57 + 527)]; local v64 = v60[3 - 1]; local v65 = v60[3]; return function(...)
            local v66 = v63; local v67 = v64; local v68 = v65; local v69 = v27; local v70 = 1 + 0; local v71 = -(1428 - (41 + 1386)); local v72 = {}; local v73 = { ... }; local v74 =
            v12("#", ...) - (1 + 0); local v75 = {}; local v76 = {}; for v85 = 0 - 0, v74 do if ((v85 >= v68) or (502 > 1529)) then v72[v85 - v68] =
                    v73[v85 + (2 - 1)]; else v76[v85] = v73[v85 + 1 + 0]; end end
            local v77 = (v74 - v68) + (167 - (122 + 44)); local v78; local v79; while true do
                local v86 = 0 - 0; while true do
                    if ((518 < 902) and (v86 == (1551 - (1126 + 425)))) then
                        v78 = v66[v70]; v79 = v78[3 - 2]; v86 = 1 + 0 + 0;
                    end
                    if ((2994 > 858) and (v86 == 1)) then
                        if (v79 <= (1123 - (118 + 1003))) then if (v79 <= ((0 - 0) + 0)) then do return; end elseif ((v79 > (1 - 0)) or (3755 <= 915)) then v76[v78[67 - (30 + (1237 - (373 + 829)))]] =
                                v62[v78[3 + 0]]; else
                                local v116; v76[v78[(1990 - (476 + 255)) - (1043 + (1344 - (369 + 761)))]] = {}; v70 =
                                v70 + (3 - 2); v78 = v66[v70]; v76[v78[2 + 0]] = v62[v78[3]]; v70 = v70 + 1 + 0 + 0; v78 =
                                v66[v70]; v76[v78[1214 - (323 + 889)]] = v78[7 - 4]; v70 = v70 +
                                (581 - (361 + (397 - 178))); v78 = v66[v70]; v116 = v78[322 - (53 + 267)]; v76[v116] =
                                v76[v116](v76[v116 + 1 + 0]); v70 = v70 + 1 + 0; v78 = v66[v70]; v76[v78[5 - 3]](); v70 =
                                v70 + (414 - (15 + (753 - 355))); v78 = v66[v70]; do return; end
                            end elseif (v79 <= (986 - (18 + 964))) then if (v79 > (11 - 8)) then v76[v78[2 + 0]] = v78
                                [2 + 1]; else v76[v78[(1090 - (64 + 174)) - (20 + 830)]] = {}; end elseif (v79 > (4 + 1)) then
                            local v128 = v78[(19 + 109) - (116 + 10)]; v76[v128] = v76[v128](v76[v128 + 1 + 0]);
                        else v76[v78[2]](); end
                        v70 = v70 + (739 - (542 + 196)); break;
                    end
                end
            end
        end;
    end
    return v29(v28(), {}, v17)(...);
end
return v15(
"LOL!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",
    v9(), ...);
