-- REAPER Script: Comprehensive Silence Processor
-- 描述: 综合静音处理 - 处理音频块之间的间隙和音频块内部的静音
-- 作者: Assistant
-- 版本: 1.0
-- 结合了 silence_gap_adjuster.lua 和 audio_silence_processor.lua 的功能

-- 获取REAPER系统瞬态检测阈值设置
function get_reaper_transient_threshold()
    local ini_file = reaper.get_ini_file()
    if not ini_file then return -60 end
    
    local file = io.open(ini_file, "r")
    if not file then return -60 end
    
    local threshold = -60
    for line in file:lines() do
        local value = line:match("transientthreshold=([%-%.%d]+)")
        if value then
            threshold = tonumber(value) or -60
            break
        end
    end
    file:close()
    
    return threshold
end

-- 获取用户输入的处理参数
function get_user_parameters()
    local default_threshold = get_reaper_transient_threshold()
    
    local retval, user_input = reaper.GetUserInputs(
        "静音处理参数设置", 4,
        "音频块间隙目标时长(秒,0=不处理):,音频块内部静音目标时长(秒,0=不处理):,静音检测阈值(dB):,添加调整标记(1=是,0=否):",
        "0,1.2," .. tostring(default_threshold) .. ",1"
    )
    
    if not retval then
        return nil -- 用户取消
    end
    
    local gap_duration, internal_duration, silence_threshold, add_markers = user_input:match("([^,]+),([^,]+),([^,]+),([^,]+)")
    gap_duration = tonumber(gap_duration) or 0
    internal_duration = tonumber(internal_duration) or 0
    silence_threshold = tonumber(silence_threshold) or default_threshold
    add_markers = tonumber(add_markers) or 1
    
    return {
        target_gap_duration = gap_duration,
        min_silence_duration = internal_duration,
        silence_threshold = silence_threshold,
        process_item_gaps = gap_duration > 0,
        process_internal_silence = internal_duration > 0,
        add_adjustment_markers = add_markers == 1
    }
end

-- 配置参数 (将由用户输入设置)
local SILENCE_THRESHOLD = -60 -- 音频块内静音检测的分贝阈值
local MIN_SILENCE_DURATION = 1.2 -- 将被用户输入覆盖
local TARGET_GAP_DURATION = 1.2 -- 将被用户输入覆盖
local SAMPLE_RATE = 44100 -- 默认采样率
local ANALYSIS_BLOCK_SIZE = 1024 -- 每个分析块的采样数
local PROCESS_INTERNAL_SILENCE = true -- 将被用户输入覆盖
local PROCESS_ITEM_GAPS = true -- 将被用户输入覆盖

-- 静音处理缓冲时间现在由用户输入提供

-- 轻量级进度显示函数
function show_simple_progress(message)
    reaper.ShowConsoleMsg(message .. "\n")
end

-- ============================================================================
-- 第一部分: 处理音频块之间间隙的函数
-- ============================================================================

-- 获取区域内所有媒体项的函数 (包含详细信息)
function get_items_in_region(region_start, region_end)
    local items = {}
    local track_count = reaper.CountTracks(0)
    
    for i = 0, track_count - 1 do
        local track = reaper.GetTrack(0, i)
        local item_count = reaper.CountTrackMediaItems(track)
        
        for j = 0, item_count - 1 do
            local item = reaper.GetTrackMediaItem(track, j)
            local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
            local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
            local item_end = item_pos + item_length
            
            -- 检查项目是否与区域重叠
            if item_pos < region_end and item_end > region_start then
                table.insert(items, {
                    item = item,
                    pos = item_pos,
                    end_pos = item_end,
                    length = item_length
                })
            end
        end
    end
    
    -- 按位置排序项目
    table.sort(items, function(a, b) return a.pos < b.pos end)
    
    return items
end

-- 查找音频块之间静音间隙的函数
function find_silence_gaps(items, region_start, region_end)
    local gaps = {}
    
    -- 如果没有项目或只有一个项目，则无间隙可检查
    if #items <= 1 then
        return gaps
    end
    
    -- 仅检查项目之间的间隙
    for i = 1, #items - 1 do
        local current_item = items[i]
        local next_item = items[i + 1]
        
        if next_item.pos > current_item.end_pos then
            local gap_duration = next_item.pos - current_item.end_pos
            if gap_duration > TARGET_GAP_DURATION then
                table.insert(gaps, {
                    start_pos = current_item.end_pos,
                    end_pos = next_item.pos,
                    duration = gap_duration
                })
            end
        end
    end
    
    return gaps
end

-- 向前移动项目的函数 (用于间隙调整)
function move_items_forward(items, from_pos, offset)
    local moved_count = 0
    
    -- 清除当前选择
    reaper.SelectAllMediaItems(0, false)
    
    -- 移动项目
    for _, item_info in ipairs(items) do
        if item_info.pos >= from_pos then
            local new_pos = item_info.pos + offset
            reaper.SetMediaItemInfo_Value(item_info.item, "D_POSITION", new_pos)
            moved_count = moved_count + 1
        end
    end
    
    if moved_count > 0 then
        reaper.UpdateArrange()
    end
    
    -- 清除选择
    reaper.SelectAllMediaItems(0, false)
    
    return moved_count
end

-- ============================================================================
-- 第二部分: 处理音频块内部静音的函数
-- ============================================================================

-- 从媒体项获取音频数据的函数
function get_audio_data(item)
    local take = reaper.GetActiveTake(item)
    if not take then return nil end
    
    local source = reaper.GetMediaItemTake_Source(take)
    if not source then return nil end
    
    local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
    local sample_rate = reaper.GetMediaSourceSampleRate(source)
    local num_channels = reaper.GetMediaSourceNumChannels(source)
    
    return {
        take = take,
        source = source,
        length = item_length,
        sample_rate = sample_rate or SAMPLE_RATE,
        channels = num_channels or 1
    }
end

-- 分析音频以检测项目内静音的函数（原始版本，应用缓冲）
function analyze_audio_silence(item, silence_threshold)
    -- 保留原有函数以兼容其他部分
    return analyze_audio_silence_raw(item, silence_threshold)
end

-- 分析音频以检测项目内静音的函数（不应用缓冲，返回原始静音段）
function analyze_audio_silence_raw(item, silence_threshold)
    local audio_data = get_audio_data(item)
    if not audio_data then return {} end
    
    local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
    local silence_segments = {}
    
    -- 获取音频访问器采样数
    local accessor = reaper.CreateTakeAudioAccessor(audio_data.take)
    if not accessor then return {} end
    
    local total_samples = math.floor(audio_data.length * audio_data.sample_rate)
    local samples_per_block = ANALYSIS_BLOCK_SIZE
    local current_silence_start = nil
    local threshold_linear = 10 ^ ((silence_threshold or SILENCE_THRESHOLD) / 20) -- 将分贝转换为线性值
    
    for sample_pos = 0, total_samples - samples_per_block, samples_per_block do
        local audio_buffer = reaper.new_array(samples_per_block * audio_data.channels)
        
        -- 获取音频采样
        local samples_read = reaper.GetAudioAccessorSamples(
            accessor, 
            audio_data.sample_rate, 
            audio_data.channels, 
            sample_pos / audio_data.sample_rate, 
            samples_per_block, 
            audio_buffer
        )
        
        if samples_read > 0 then
            -- 计算此块的RMS值
            local sum_squares = 0
            local buffer_table = audio_buffer.table()
            
            for i = 1, samples_read * audio_data.channels do
                sum_squares = sum_squares + buffer_table[i] * buffer_table[i]
            end
            
            local rms = math.sqrt(sum_squares / (samples_read * audio_data.channels))
            local time_pos = sample_pos / audio_data.sample_rate
            
            -- 检查此块是否为静音
            if rms < threshold_linear then
                if not current_silence_start then
                    current_silence_start = time_pos
                end
            else
                if current_silence_start then
                    local silence_duration = time_pos - current_silence_start
                    if silence_duration >= MIN_SILENCE_DURATION then
                        -- 不应用缓冲时间，返回原始静音段
                        table.insert(silence_segments, {
                            start_time = item_pos + current_silence_start,
                            end_time = item_pos + time_pos,
                            duration = silence_duration,
                            item_relative_start = current_silence_start,
                            item_relative_end = time_pos
                        })
                    end
                    current_silence_start = nil
                end
            end
        end
        
        audio_buffer.clear()
    end
    
    -- 处理延伸到项目末尾的静音
    if current_silence_start then
        local silence_duration = audio_data.length - current_silence_start
        if silence_duration >= MIN_SILENCE_DURATION then
            -- 不应用缓冲时间，返回原始静音段
            table.insert(silence_segments, {
                start_time = item_pos + current_silence_start,
                end_time = item_pos + audio_data.length,
                duration = silence_duration,
                item_relative_start = current_silence_start,
                item_relative_end = audio_data.length
            })
        end
    end
    
    reaper.DestroyAudioAccessor(accessor)
    return silence_segments
end

-- 在静音点分割音频项目的函数
function split_item_at_silence(item, silence_segments, add_markers)
    if #silence_segments == 0 then return {item} end
    
    local split_items = {}
    local original_item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
    
    -- 按开始时间排序静音段 (逆序以便从右到左处理)
    table.sort(silence_segments, function(a, b) return a.item_relative_start > b.item_relative_start end)
    
    -- 从右到左处理静音段以避免位置冲突
    local current_item = item
    
    for i, silence in ipairs(silence_segments) do
        if current_item then
            -- Split at the end of silence first
            local silence_end_pos = original_item_pos + silence.item_relative_end
            local after_silence_item = reaper.SplitMediaItem(current_item, silence_end_pos)
            
            -- Split at the start of silence
            local silence_start_pos = original_item_pos + silence.item_relative_start
            local silence_item = reaper.SplitMediaItem(current_item, silence_start_pos)
            
            -- 根据用户选择决定是否添加标记
            if add_markers then
                -- 在分割点添加标记，显示静音调整信息
                local original_duration = silence.original_duration or silence.duration
                -- 调整计算应该基于原始静音时长和目标时长的差值，不受缓冲时间影响
                local target_duration = MIN_SILENCE_DURATION
                local time_saved = math.max(0, original_duration - target_duration)
                local marker_name = string.format("静音%.2fs(调整%.2fs)", original_duration, time_saved)
                
                -- 在静音开始位置添加标记
                reaper.AddProjectMarker2(0, false, silence_start_pos, 0, marker_name, -1, 0)
            end
            
            -- Delete the silence part
            if silence_item then
                reaper.DeleteTrackMediaItem(reaper.GetMediaItem_Track(silence_item), silence_item)
            end
        end
    end
    
    -- Collect all remaining items on the track that were part of the original item
    local track = reaper.GetMediaItem_Track(item)
    local item_count = reaper.CountTrackMediaItems(track)
    
    for i = 0, item_count - 1 do
        local track_item = reaper.GetTrackMediaItem(track, i)
        local track_item_pos = reaper.GetMediaItemInfo_Value(track_item, "D_POSITION")
        local track_item_end = track_item_pos + reaper.GetMediaItemInfo_Value(track_item, "D_LENGTH")
        local original_item_end = original_item_pos + reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
        
        -- Check if this item is within the original item's range
        if track_item_pos >= original_item_pos and track_item_pos < original_item_end then
            table.insert(split_items, track_item)
        end
    end
    
    -- Sort split items by position
    table.sort(split_items, function(a, b)
        return reaper.GetMediaItemInfo_Value(a, "D_POSITION") < reaper.GetMediaItemInfo_Value(b, "D_POSITION")
    end)
    
    return split_items
end

-- 简化的静音切割函数 - 按照用户逻辑实现
function split_item_at_silence_simple(item, processed_segments, add_markers)
    if #processed_segments == 0 then return {item} end
    
    local original_item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
    
    -- 按开始时间排序静音段 (逆序以便从右到左处理)
    table.sort(processed_segments, function(a, b) return a.item_relative_start > b.item_relative_start end)
    
    -- 从右到左处理静音段以避免位置冲突
    local current_item = item
    
    for i, segment in ipairs(processed_segments) do
        if current_item then
            -- 在静音结束位置切割
            local silence_end_pos = original_item_pos + segment.item_relative_end
            local after_silence_item = reaper.SplitMediaItem(current_item, silence_end_pos)
            
            -- 在静音开始位置切割
            local silence_start_pos = original_item_pos + segment.item_relative_start
            local silence_item = reaper.SplitMediaItem(current_item, silence_start_pos)
            
            -- 根据用户选择决定是否添加标记
             if add_markers then
                 local marker_name = string.format("静音%.2fs(缩短%.2fs)", segment.original_duration or 0, segment.time_removed or 0)
                 reaper.AddProjectMarker2(0, false, silence_start_pos, 0, marker_name, -1, 0)
             end
            
            -- 删除静音部分
            if silence_item then
                reaper.DeleteTrackMediaItem(reaper.GetMediaItem_Track(silence_item), silence_item)
            end
        end
    end
    
    return {current_item} -- 返回处理后的项目
end

-- 移动后续音频块的函数
function move_subsequent_items(original_item_end, time_to_remove, region_start, region_end)
    local track_count = reaper.CountTracks(0)
    local moved_count = 0
    
    for i = 0, track_count - 1 do
        local track = reaper.GetTrack(0, i)
        local item_count = reaper.CountTrackMediaItems(track)
        
        for j = 0, item_count - 1 do
            local other_item = reaper.GetTrackMediaItem(track, j)
            local other_pos = reaper.GetMediaItemInfo_Value(other_item, "D_POSITION")
            local other_end = other_pos + reaper.GetMediaItemInfo_Value(other_item, "D_LENGTH")
            
            -- 检查项目是否在区域内且在原始项目结束位置之后
            local item_in_region = (other_pos >= region_start and other_pos < region_end) or 
                                 (other_pos < region_end and other_end > region_start)
            
            if item_in_region and other_pos >= original_item_end then
                local new_pos = other_pos - time_to_remove
                -- 确保移动的项目不会超出区域边界
                if new_pos >= region_start then
                    reaper.SetMediaItemInfo_Value(other_item, "D_POSITION", new_pos)
                    moved_count = moved_count + 1
                end
            end
        end
    end
    
    if moved_count > 0 then
        reaper.UpdateArrange()
        reaper.ShowConsoleMsg(string.format("    向前移动了 %d 个后续音频块，移动距离 %.2f 秒\n", moved_count, time_to_remove))
    end
    
    return moved_count
end

-- Function to adjust gaps between split items
function adjust_gaps_between_items(items)
    if #items <= 1 then return 0 end
    
    -- Sort items by position
    table.sort(items, function(a, b)
        return reaper.GetMediaItemInfo_Value(a, "D_POSITION") < reaper.GetMediaItemInfo_Value(b, "D_POSITION")
    end)
    
    local total_adjustment = 0
    
    for i = 1, #items - 1 do
        local current_item = items[i]
        local next_item = items[i + 1]
        
        local current_end = reaper.GetMediaItemInfo_Value(current_item, "D_POSITION") + 
                           reaper.GetMediaItemInfo_Value(current_item, "D_LENGTH")
        local next_start = reaper.GetMediaItemInfo_Value(next_item, "D_POSITION")
        
        local current_gap = next_start - current_end
        -- 对于分割后的音频块间隙，应该使用MIN_SILENCE_DURATION而不是TARGET_GAP_DURATION
        local target_gap = MIN_SILENCE_DURATION
        local gap_adjustment = current_gap - target_gap
        
        if math.abs(gap_adjustment) > 0.01 then -- Only adjust if significant difference
            reaper.ShowConsoleMsg(string.format("    间隙调整：当前间隙 %.3f 秒，目标间隙 %.3f 秒，调整量 %.3f 秒\n", 
                current_gap, target_gap, gap_adjustment))
            
            -- Move the next item and all subsequent items
            -- If gap_adjustment > 0, we need to move items forward (reduce gap)
            -- If gap_adjustment < 0, we need to move items backward (increase gap)
            for j = i + 1, #items do
                local item_to_move = items[j]
                local current_pos = reaper.GetMediaItemInfo_Value(item_to_move, "D_POSITION")
                local new_pos = current_pos - gap_adjustment
                reaper.SetMediaItemInfo_Value(item_to_move, "D_POSITION", new_pos)
                reaper.ShowConsoleMsg(string.format("      移动项目 %d：从 %.3f 到 %.3f 秒\n", j, current_pos, new_pos))
            end
            
            total_adjustment = total_adjustment + gap_adjustment
        end
    end
    
    return total_adjustment
end

-- ============================================================================
-- SECTION 3: Combined processing functions
-- ============================================================================

-- Function to get all audio items in a region (audio only)
function get_audio_items_in_region(region_start, region_end)
    local items = {}
    local track_count = reaper.CountTracks(0)
    
    for i = 0, track_count - 1 do
        local track = reaper.GetTrack(0, i)
        local item_count = reaper.CountTrackMediaItems(track)
        
        for j = 0, item_count - 1 do
            local item = reaper.GetTrackMediaItem(track, j)
            local item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
            local item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
            local item_end = item_pos + item_length
            
            -- 检查项目是否与区域重叠
            if item_pos < region_end and item_end > region_start then
                -- Only process audio items (not MIDI)
                local take = reaper.GetActiveTake(item)
                if take and reaper.TakeIsMIDI(take) == false then
                    table.insert(items, {item = item, pos = item_pos})
                end
            end
        end
    end
    
    -- 按时间位置排序音频项目
    table.sort(items, function(a, b) return a.pos < b.pos end)
    
    -- 提取排序后的项目
    local sorted_items = {}
    for _, item_data in ipairs(items) do
        table.insert(sorted_items, item_data.item)
    end
    
    return sorted_items
end

-- 处理单个音频项目内部静音的函数 - 重新设计以符合用户逻辑
function process_audio_item_internal_silence(item, region_start, region_end, silence_threshold, add_markers)
    local original_item_pos = reaper.GetMediaItemInfo_Value(item, "D_POSITION")
    local original_item_length = reaper.GetMediaItemInfo_Value(item, "D_LENGTH")
    local original_item_end = original_item_pos + original_item_length
    
    reaper.ShowConsoleMsg(string.format("[DEBUG] 处理音频块: %.3f - %.3f (长度: %.3f)\n", 
        original_item_pos, original_item_end, original_item_length))
    
    -- 步骤1: 分析静音（获取原始静音段）
    local silence_segments = analyze_audio_silence_raw(item, silence_threshold)
    
    if #silence_segments == 0 then
        reaper.ShowConsoleMsg("[DEBUG] 未找到静音段\n")
        return 0, 0
    end
    
    reaper.ShowConsoleMsg(string.format("[DEBUG] 找到 %d 个静音段\n", #silence_segments))
    
    local total_time_saved = 0
    local processed_segments_count = 0
    
    -- 从右到左处理静音段，避免位置变化影响
    for i = #silence_segments, 1, -1 do
        local silence = silence_segments[i]
        local original_duration = silence.duration
        
        if original_duration > MIN_SILENCE_DURATION then
            local time_to_remove = original_duration - MIN_SILENCE_DURATION
            
            reaper.ShowConsoleMsg(string.format("[DEBUG] 处理静音段 %d: %.3f-%.3f, 原始长度: %.3f, 目标长度: %.3f, 缩短: %.3f\n",
                i, silence.item_relative_start, silence.item_relative_end, original_duration, MIN_SILENCE_DURATION, time_to_remove))
            
            -- 计算切割位置：静音中间位置
            local silence_middle = silence.item_relative_start + (original_duration / 2)
            local cut_start_pos = original_item_pos + silence_middle - (time_to_remove / 2)
            local cut_end_pos = original_item_pos + silence_middle + (time_to_remove / 2)
            
            reaper.ShowConsoleMsg(string.format("[DEBUG] 静音中间位置: %.3f, 切割范围: %.3f - %.3f (长度: %.3f)\n",
                silence_middle, cut_start_pos, cut_end_pos, time_to_remove))
            
            -- 在静音中间切割出需要删除的部分
            local middle_item = reaper.SplitMediaItem(item, cut_start_pos)
            
            if middle_item then
                local right_item = reaper.SplitMediaItem(middle_item, cut_end_pos)
                
                if right_item then
                    -- 删除中间的静音部分
                    reaper.DeleteTrackMediaItem(reaper.GetMediaItem_Track(middle_item), middle_item)
                    
                    -- 将右边的项目向前移动time_to_remove的距离
                    local right_item_pos = reaper.GetMediaItemInfo_Value(right_item, "D_POSITION")
                    reaper.SetMediaItemInfo_Value(right_item, "D_POSITION", right_item_pos - time_to_remove)
                    
                    reaper.ShowConsoleMsg(string.format("[DEBUG] 删除中间静音，移动右边项目从 %.3f 到 %.3f\n",
                        right_item_pos, right_item_pos - time_to_remove))
                    
                    -- 添加标记（如果需要）
                    if add_markers then
                        local marker_pos = cut_start_pos
                        local marker_text = string.format("静音缩短: %.3fs -> %.3fs (节省 %.3fs)", 
                            original_duration, MIN_SILENCE_DURATION, time_to_remove)
                        reaper.AddProjectMarker2(0, false, marker_pos, 0, marker_text, -1, 0)
                    end
                    
                    total_time_saved = total_time_saved + time_to_remove
                    processed_segments_count = processed_segments_count + 1
                    
                    -- 更新item引用为右边的项目，继续处理
                    item = right_item
                else
                    reaper.ShowConsoleMsg("[DEBUG] 第二次切割失败\n")
                end
            else
                reaper.ShowConsoleMsg("[DEBUG] 第一次切割失败\n")
            end
        end
    end
    
    -- 移动后续的所有音频块
    if total_time_saved > 0 then
        move_subsequent_items(original_item_end, total_time_saved, region_start, region_end)
    end
    
    reaper.ShowConsoleMsg(string.format("处理音频块 %.2f 秒：处理了 %d 个静音段，总计缩短 %.2f 秒\n", 
        original_item_pos, processed_segments_count, total_time_saved))
    
    return processed_segments_count, total_time_saved
end

-- 综合处理单个区域的函数
function process_region_comprehensive(region_start, region_end, region_index, region_name, add_markers)
    local total_gaps_adjusted = 0
    local total_silence_segments = 0
    local total_time_saved = 0
    local region_time_saved = 0
    local item_counter = 0
    
    -- 步骤1: 处理音频项目内部的静音 (如果启用)
    if PROCESS_INTERNAL_SILENCE then
        local audio_items = get_audio_items_in_region(region_start, region_end)
        
        if #audio_items > 0 then
            for _, item in ipairs(audio_items) do
                item_counter = item_counter + 1
                local segments, time_saved = process_audio_item_internal_silence(item, region_start, region_end, SILENCE_THRESHOLD, add_markers)
                if segments > 0 then
                    -- 获取音频项目所在的轨道编号
                    local track = reaper.GetMediaItem_Track(item)
                    local track_number = reaper.GetMediaTrackInfo_Value(track, "IP_TRACKNUMBER")
                    reaper.ShowConsoleMsg(string.format("区间 %d 第 %d 个音频块 轨道 %d：", region_index, item_counter, track_number))
                end
                total_silence_segments = total_silence_segments + segments
                total_time_saved = total_time_saved + time_saved
            end
        end
    end
    
    -- 步骤2: 处理音频项目之间的间隙 (如果启用)
    if PROCESS_ITEM_GAPS then
        -- 获取潜在分割后的更新项目列表
        local items = get_items_in_region(region_start, region_end)
        local gaps = find_silence_gaps(items, region_start, region_end)
        
        if #gaps > 0 then
            -- 从右到左处理间隙以避免位置冲突
            for i = #gaps, 1, -1 do
                local gap = gaps[i]
                local reduction = gap.duration - TARGET_GAP_DURATION
                
                if reduction > 0 then
                    -- 将此间隙后的项目向前移动
                    local moved_count = move_items_forward(items, gap.end_pos, -reduction)
                    
                    region_time_saved = region_time_saved + reduction
                    total_gaps_adjusted = total_gaps_adjusted + 1
                    
                    reaper.ShowConsoleMsg(string.format(
                        "区间 %d 调整间隙 %.2f-%.2f 秒，节省 %.2f 秒\n",
                        region_index, gap.start_pos, gap.end_pos, reduction
                    ))
                    
                    -- 为下一次迭代更新项目位置
                    for j, item_info in ipairs(items) do
                        if item_info.pos >= gap.end_pos then
                            items[j].pos = item_info.pos - reduction
                            items[j].end_pos = item_info.end_pos - reduction
                        end
                    end
                end
            end
            
            -- 如有必要调整区域结束位置
            if region_time_saved > 0 then
                local new_region_end = region_end - region_time_saved
                reaper.SetProjectMarker(region_index, true, region_start, new_region_end, "")
            end
        end
    end
    
    total_time_saved = total_time_saved + region_time_saved
    
    return total_gaps_adjusted, total_silence_segments, total_time_saved
end

-- ============================================================================
-- 第四部分: 主函数
-- ============================================================================

-- 主函数
function main()
    -- 获取用户输入参数
    local params = get_user_parameters()
    if not params then
        return -- 用户取消
    end
    
    -- 根据用户输入更新全局配置
    TARGET_GAP_DURATION = params.target_gap_duration
    MIN_SILENCE_DURATION = params.min_silence_duration
    SILENCE_THRESHOLD = params.silence_threshold
    PROCESS_ITEM_GAPS = params.process_item_gaps
    PROCESS_INTERNAL_SILENCE = params.process_internal_silence
    
    -- 检查用户是否要处理任何内容
    if not PROCESS_ITEM_GAPS and not PROCESS_INTERNAL_SILENCE then
        reaper.ShowConsoleMsg("未选择任何处理选项，操作取消。\n")
        return
    end
    
    reaper.Undo_BeginBlock()
    
    -- 显示开始信息
    show_simple_progress("=== 开始综合静音处理 ===")
    
    local region_count = reaper.CountProjectMarkers(0)
    local actual_region_count = 0
    
    -- 计算实际区域数量
    for i = 0, region_count - 1 do
        local retval, isrgn = reaper.EnumProjectMarkers(i)
        if isrgn then
            actual_region_count = actual_region_count + 1
        end
    end
    
    if actual_region_count == 0 then
        show_simple_progress("未找到任何区域，操作取消")
        reaper.Undo_EndBlock("Comprehensive silence processing", -1)
        return
    end
    
    show_simple_progress(string.format("找到 %d 个区域，开始处理...", actual_region_count))
    
    local total_gaps_adjusted = 0
    local total_silence_segments = 0
    local total_time_saved = 0
    local processed_count = 0
    
    -- 处理每个区域
    for i = 0, region_count - 1 do
        local retval, isrgn, pos, rgnend, name, markrgnindexnumber = reaper.EnumProjectMarkers(i)
        
        if isrgn then
            processed_count = processed_count + 1
            show_simple_progress(string.format("[%d/%d] 处理区域 %d: %s", 
                processed_count, actual_region_count, markrgnindexnumber, name or "未命名"))
            
            local gaps_adjusted, silence_segments, time_saved = process_region_comprehensive(
                pos, rgnend, markrgnindexnumber, name, params.add_adjustment_markers
            )
            
            total_gaps_adjusted = total_gaps_adjusted + gaps_adjusted
            total_silence_segments = total_silence_segments + silence_segments
            total_time_saved = total_time_saved + time_saved
            
            if gaps_adjusted > 0 or silence_segments > 0 then
                show_simple_progress(string.format("  完成: 调整%d个间隙, 处理%d个静音段, 节省%.2f秒", 
                    gaps_adjusted, silence_segments, time_saved))
            end
        end
    end
    
    -- 最终更新
    show_simple_progress("正在更新界面...")
    reaper.UpdateArrange()
    reaper.TrackList_AdjustWindows(false)
    
    -- 显示处理结果
    show_simple_progress("=== 处理完成 ===")
    show_simple_progress(string.format("处理区域: %d 个", processed_count))
    show_simple_progress(string.format("调整间隙: %d 个", total_gaps_adjusted))
    show_simple_progress(string.format("处理静音段: %d 个", total_silence_segments))
    show_simple_progress(string.format("总计节省时间: %.2f 秒", total_time_saved))
    
    if total_gaps_adjusted > 0 or total_silence_segments > 0 then
        show_simple_progress("✓ 静音处理成功完成！")
    else
        show_simple_progress("! 未发现需要处理的静音内容")
    end
    
    reaper.Undo_EndBlock("Comprehensive silence processing", -1)
end

-- 运行主函数
main()