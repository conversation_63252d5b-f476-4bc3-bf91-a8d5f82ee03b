# 句子点击跳转CV角色列表功能详解

## 概述

在`mark.lua`脚本中，实现了一个智能的用户交互功能：当用户点击句子列表中的某个句子时，系统会自动识别该句子对应的CV（配音演员）和角色信息，并自动滚动到CV角色列表中的对应位置，实现快速定位和关联显示。

## 功能特点

- **智能识别**：自动从句子内容中提取CV和角色信息
- **自动跳转**：点击句子后立即跳转到对应的CV角色位置
- **精确定位**：将目标项显示在可见区域的中间位置
- **支持分类显示**：按CV分类整理角色列表
- **响应式交互**：支持左键单选和右键多选模式

## 核心代码结构

### 1. 主要功能函数

#### 1.1 句子点击事件处理（第2890-3089行）

```lua
-- 检查内容区域点击（不包括滚动条区域）
if is_point_in_rect(mouse_x, mouse_y, content_area) and
   not is_point_in_rect(mouse_x, mouse_y, sentence_scrollbar) then
  -- 如果有悬停的句子，则选择该句子
  if hover_sentence_idx > 0 and hover_sentence_idx <= #sentences then
    -- 检测当前鼠标按键状态 (左键=1, 右键=2)
    local mouse_cap = gfx.mouse_cap
    
    -- 确定是左键还是右键点击
    local is_left_click = (mouse_cap & 1) == 1
    local is_right_click = (mouse_cap & 2) == 2
    
    -- 处理左键点击
    if is_left_click then
      -- 左键点击，进入单选模式
      is_ctrl_down = false
      
      -- 设置选中文本
      selected_text = sentences[hover_sentence_idx]
      
      -- 左键单选模式下，清空多选列表
      selected_indices = {hover_sentence_idx}
      selected_texts = {selected_text}
      
      -- 提取CV和角色信息
      if text_utils and text_utils.handle_cv_role_selection then
        -- 使用新的统一处理函数
        selected_role, selected_cv = text_utils.handle_cv_role_selection(
          selected_text,
          cv_role_pairs,
          selected_role,
          selected_cv,
          is_cv_role_reversed
        )
        -- 自动跳转到对应的角色CV位置
        scroll_to_cv_role(selected_role, selected_cv)
      end
    end
  end
end
```

**功能说明：**
- 检测鼠标点击位置是否在内容区域内
- 识别点击的是哪个句子（通过`hover_sentence_idx`）
- 区分左键和右键点击，执行不同的处理逻辑
- 调用`text_utils.handle_cv_role_selection`提取CV和角色信息
- 调用`scroll_to_cv_role`执行跳转

#### 1.2 跳转核心函数 `scroll_to_cv_role`（第362-420行）

```lua
function scroll_to_cv_role(target_cv, target_role)
  if not target_cv or not target_role or not cv_role_pairs then
    return
  end
  
  -- 获取CV角色分类数据
  local cv_categories, cv_order = get_cv_role_categories(cv_role_pairs, is_cv_role_reversed)
  
  -- 计算目标CV角色在列表中的位置
  local target_position = 0
  local found = false
  
  for _, cv_name in ipairs(cv_order) do
    local roles = cv_categories[cv_name]
    
    -- CV分类标题占一行
    target_position = target_position + 1
    
    -- 检查是否是目标CV
    if cv_name == target_cv then
      -- 在该CV下查找目标角色
      for _, role_info in ipairs(roles) do
        if role_info.role == target_role then
          found = true
          break
        end
        target_position = target_position + 1
      end
      if found then
        break
      end
    else
      -- 跳过该CV下的所有角色
      target_position = target_position + #roles
    end
  end
  
  if not found then
    return
  end
  
  -- 计算CV角色列表的可见项目数
  local cv_role_line_height = 20
  local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)
  
  -- 计算目标滚动位置，让目标角色显示在可见区域的中间
  local target_scroll = math.max(0, target_position - math.floor(visible_items / 2))
  
  -- 计算总项目数
  local total_items = 0
  for _, roles in pairs(cv_categories) do
    total_items = total_items + 1 + #roles  -- CV标题 + 角色数量
  end
  
  -- 确保不超过最大滚动范围
  local max_scroll = math.max(0, total_items - visible_items)
  cv_role_scroll_pos = math.min(target_scroll, max_scroll)
end
```

**功能说明：**
- 接收目标CV和角色名称作为参数
- 使用`get_cv_role_categories`获取分类数据
- 遍历CV分类，计算目标项在列表中的精确位置
- 计算最佳滚动位置，使目标项显示在可见区域中间
- 更新`cv_role_scroll_pos`变量触发界面重绘

#### 1.3 辅助函数 `get_cv_role_categories`（第4955-4996行）

```lua
function get_cv_role_categories(pairs, is_reversed)
  local cv_categories = {}  -- 用于存储按CV分类的角色
  local cv_order = {}       -- 用于记录CV的顺序

  -- 遍历所有CV角色对，按CV进行分类
  -- 无论是否选择了交换位置复选框，都以相同的方式显示
  for i, pair in ipairs(pairs) do
    -- 始终保持角色在右边，CV在左边的显示方式，忽略is_reversed参数
    local cv_name = pair.cv
    local role_name = pair.role

    if not cv_categories[cv_name] then
      cv_categories[cv_name] = {}
      table.insert(cv_order, cv_name)
    end
    table.insert(cv_categories[cv_name], {role = role_name, index = i})
  end

  return cv_categories, cv_order
end
```

**功能说明：**
- 将CV角色对按CV进行分类整理
- 返回两个数据结构：`cv_categories`（分类数据）和`cv_order`（CV顺序）
- 为CV角色列表的显示和位置计算提供标准化的数据结构

### 2. 数据结构说明

#### 2.1 CV角色对数据结构

```lua
cv_role_pairs = {
  {cv = "张三", role = "主角"},
  {cv = "李四", role = "配角A"},
  {cv = "张三", role = "旁白"},
  -- ...
}
```

#### 2.2 分类后的数据结构

```lua
cv_categories = {
  ["张三"] = {
    {role = "主角", index = 1},
    {role = "旁白", index = 3}
  },
  ["李四"] = {
    {role = "配角A", index = 2}
  }
}

cv_order = {"张三", "李四"}
```

### 3. 跳转流程详解

#### 3.1 完整流程图

```
用户点击句子
    ↓
检测鼠标位置和按键状态
    ↓
确定悬停的句子索引
    ↓
提取句子内容
    ↓
调用text_utils.handle_cv_role_selection
    ↓
识别CV和角色信息
    ↓
调用scroll_to_cv_role(selected_role, selected_cv)
    ↓
获取CV角色分类数据
    ↓
计算目标位置
    ↓
计算最佳滚动位置
    ↓
更新cv_role_scroll_pos
    ↓
触发界面重绘
    ↓
显示跳转结果
```

#### 3.2 位置计算算法

1. **遍历CV分类**：按照`cv_order`的顺序遍历每个CV
2. **计算行数**：每个CV标题占1行，每个角色占1行
3. **查找目标**：在对应CV下查找目标角色
4. **计算位置**：累计前面所有项目的行数得到目标位置
5. **计算滚动**：使目标项显示在可见区域中间

```lua
-- 位置计算示例
-- 假设CV列表如下：
-- CV: 张三
--   主角
--   旁白
-- CV: 李四
--   配角A

-- 如果要跳转到"张三-旁白"：
-- 位置 = 1(张三标题) + 1(主角) + 1(旁白) = 3
-- 滚动位置 = max(0, 3 - floor(visible_items/2))
```

### 4. 相关变量说明

#### 4.1 全局变量

```lua
local cv_role_scroll_pos = 0   -- CV角色列表滚动位置
local hover_sentence_idx = -1  -- 鼠标悬停的句子索引
local selected_text = ""       -- 存储单击选择的文本内容
local selected_role = ""       -- 当前选中的角色
local selected_cv = ""         -- 当前选中的CV
local is_cv_role_reversed = false -- 是否交换CV和角色的显示位置
```

#### 4.2 UI区域定义

```lua
-- 内容区域（句子列表显示区域）
content_area = {
  x = ...,
  y = ...,
  w = ...,
  h = ...
}

-- CV角色列表区域
cv_role_list = {
  x = ...,
  y = ...,
  w = ...,
  h = ...
}
```

### 5. 扩展功能

#### 5.1 多选模式支持

右键点击可以进入多选模式，支持选择多个句子：

```lua
if is_right_click then
  -- 多选模式处理逻辑
  -- 支持Ctrl+点击的多选行为
  -- 更新选中文本显示
  -- 从第一个选中的句子获取CV角色信息
end
```

#### 5.2 对轨功能集成

当启用对轨功能时，点击句子还会触发轨道对齐操作：

```lua
if is_track_align_enabled then
  -- 检查CV是否为章节内容
  if selected_cv and selected_cv ~= "" then
    -- 执行对轨脚本或创建区间
  end
end
```

### 6. 性能优化

#### 6.1 缓存机制

- 使用`cached_sentence_heights`缓存句子高度
- 使用`cached_total_content_height`缓存内容总高度
- 避免重复计算文本换行和测量

#### 6.2 渲染优化

- 只渲染可见区域内的内容
- 使用滚动位置控制渲染范围
- 支持平滑滚动动画

### 7. 错误处理

#### 7.1 边界检查

```lua
-- 检查参数有效性
if not target_cv or not target_role or not cv_role_pairs then
  return
end

-- 检查句子索引范围
if hover_sentence_idx > 0 and hover_sentence_idx <= #sentences then
  -- 处理点击
end
```

#### 7.2 容错机制

- 当找不到目标CV或角色时，静默返回而不报错
- 滚动位置超出范围时自动修正
- 支持空数据状态的处理

### 8. 使用示例

#### 8.1 基本使用

1. 用户在句子列表中点击包含角色对话的句子
2. 系统自动识别句子中的CV和角色信息
3. CV角色列表自动滚动到对应位置并高亮显示

#### 8.2 高级功能

1. **搜索跳转**：配合搜索功能，可以搜索特定CV或角色
2. **章节导航**：结合章节功能，可以按章节过滤CV角色
3. **批量操作**：支持多选句子进行批量处理

### 9. 总结

这个功能通过精心设计的数据结构和算法，实现了句子与CV角色列表之间的智能关联。主要优势包括：

- **用户体验优秀**：点击即跳转，操作简单直观
- **性能表现良好**：使用缓存和优化算法，响应迅速
- **扩展性强**：支持多种交互模式和功能集成
- **稳定性高**：完善的错误处理和边界检查

该功能是`mark.lua`脚本中的核心交互特性之一，大大提升了用户在处理音频后期制作时的工作效率。